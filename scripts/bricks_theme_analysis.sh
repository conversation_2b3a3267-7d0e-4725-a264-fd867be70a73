#!/bin/bash

# Bricks 主题系统使用情况分析脚本
# 用于检查 Mason bricks 模板是否使用了新的主题设计

echo "🧱 Bricks 主题系统使用情况分析"
echo "=================================="

# 设置颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 项目根目录
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
BRICKS_DIR="$PROJECT_ROOT/bricks"

echo -e "${BLUE}📊 分析目录: $BRICKS_DIR${NC}"
echo ""

# 1. 统计 bricks 中的主题使用情况
echo -e "${GREEN}1. Bricks 主题使用统计${NC}"
echo "------------------------"

# 统计导入 theme_extensions.dart 的文件
THEME_IMPORTS=$(find "$BRICKS_DIR" -name "*.dart" -exec grep -l "theme_extensions.dart" {} \; | wc -l)
echo "✅ 导入主题扩展的模板文件数: $THEME_IMPORTS"

# 统计使用 context.colors 的次数
CONTEXT_COLORS=$(find "$BRICKS_DIR" -name "*.dart" -exec grep -o "context\.colors\." {} \; | wc -l)
echo "✅ context.colors 使用次数: $CONTEXT_COLORS"

# 统计使用 context.textStyles 的次数
CONTEXT_TEXT_STYLES=$(find "$BRICKS_DIR" -name "*.dart" -exec grep -o "context\.textStyles\." {} \; | wc -l)
echo "✅ context.textStyles 使用次数: $CONTEXT_TEXT_STYLES"

# 统计使用 context.themeColors 的次数
CONTEXT_THEME_COLORS=$(find "$BRICKS_DIR" -name "*.dart" -exec grep -o "context\.themeColors\." {} \; | wc -l)
echo "✅ context.themeColors 使用次数: $CONTEXT_THEME_COLORS"

echo ""

# 2. 统计旧方式使用情况
echo -e "${YELLOW}2. 待迁移的模板代码${NC}"
echo "------------------------"

# 统计硬编码颜色使用
HARDCODED_COLORS=$(find "$BRICKS_DIR" -name "*.dart" -exec grep "\bColors\.[a-zA-Z]" {} \; | grep -v "Colors.transparent" | wc -l)
echo "⚠️  硬编码颜色使用次数: $HARDCODED_COLORS"

# 统计 withOpacity 使用
WITH_OPACITY=$(find "$BRICKS_DIR" -name "*.dart" -exec grep -o "\.withOpacity(" {} \; | wc -l)
echo "⚠️  withOpacity 使用次数: $WITH_OPACITY"

# 统计 Theme.of(context) 直接使用
THEME_OF_CONTEXT=$(find "$BRICKS_DIR" -name "*.dart" -exec grep -o "Theme\.of(context)" {} \; | wc -l)
echo "⚠️  Theme.of(context) 直接使用次数: $THEME_OF_CONTEXT"

echo ""

# 3. 问题文件识别
echo -e "${RED}3. 需要更新的模板文件${NC}"
echo "------------------------"

echo "🔍 包含硬编码颜色的模板文件:"
find "$BRICKS_DIR" -name "*.dart" -exec sh -c 'count=$(grep "\bColors\.[a-zA-Z]" "$1" | grep -v "Colors.transparent" | wc -l); if [ $count -gt 0 ]; then echo "  - $1 ($count 处)"; fi' _ {} \;

echo ""
echo "🔍 仍在使用 withOpacity 的模板文件:"
find "$BRICKS_DIR" -name "*.dart" -exec sh -c 'count=$(grep -o "\.withOpacity(" "$1" | wc -l); if [ $count -gt 0 ]; then echo "  - $1 ($count 处)"; fi' _ {} \;

echo ""
echo "🔍 仍在使用 Theme.of(context) 的模板文件:"
find "$BRICKS_DIR" -name "*.dart" -exec sh -c 'count=$(grep -o "Theme\.of(context)" "$1" | wc -l); if [ $count -gt 0 ]; then echo "  - $1 ($count 处)"; fi' _ {} \;

echo ""

# 4. 总体评估
echo -e "${BLUE}4. Bricks 主题现代化评估${NC}"
echo "------------------------"

TOTAL_DART_FILES=$(find "$BRICKS_DIR" -name "*.dart" | wc -l)
echo "📁 总 Dart 模板文件数: $TOTAL_DART_FILES"

if [ $TOTAL_DART_FILES -gt 0 ]; then
    if [ $THEME_IMPORTS -gt 0 ]; then
        MODERNIZATION_RATE=$((THEME_IMPORTS * 100 / TOTAL_DART_FILES))
        echo "📈 主题现代化率: ${MODERNIZATION_RATE}%"
    else
        echo "📈 主题现代化率: 0%"
    fi
fi

echo ""

# 5. 建议和总结
echo -e "${GREEN}5. 改进建议${NC}"
echo "------------------------"

if [ $THEME_OF_CONTEXT -gt 0 ]; then
    echo "💡 建议更新模板文件使用 context 扩展替代 Theme.of(context)"
fi

if [ $HARDCODED_COLORS -gt 0 ]; then
    echo "💡 建议更新模板文件使用语义化颜色替代硬编码颜色"
fi

if [ $WITH_OPACITY -gt 0 ]; then
    echo "💡 建议更新模板文件使用新的透明度方法替代 withOpacity"
fi

if [ $THEME_IMPORTS -eq 0 ] && [ $TOTAL_DART_FILES -gt 0 ]; then
    echo "💡 建议在模板文件中添加主题扩展导入"
fi

echo ""
echo -e "${GREEN}✅ Bricks 分析完成！${NC}"
echo "=================================="

# 生成报告文件
REPORT_FILE="$PROJECT_ROOT/bricks_theme_report_$(date +%Y%m%d_%H%M%S).txt"
{
    echo "Bricks 主题系统使用情况报告"
    echo "生成时间: $(date)"
    echo "=================================="
    echo ""
    echo "主题扩展使用统计:"
    echo "- 导入主题扩展的模板文件数: $THEME_IMPORTS"
    echo "- context.colors 使用次数: $CONTEXT_COLORS"
    echo "- context.textStyles 使用次数: $CONTEXT_TEXT_STYLES"
    echo "- context.themeColors 使用次数: $CONTEXT_THEME_COLORS"
    echo ""
    echo "待迁移代码统计:"
    echo "- 硬编码颜色使用次数: $HARDCODED_COLORS"
    echo "- withOpacity 使用次数: $WITH_OPACITY"
    echo "- Theme.of(context) 直接使用次数: $THEME_OF_CONTEXT"
    echo ""
    echo "总体评估:"
    echo "- 总 Dart 模板文件数: $TOTAL_DART_FILES"
    if [ $TOTAL_DART_FILES -gt 0 ] && [ $THEME_IMPORTS -gt 0 ]; then
        echo "- 主题现代化率: ${MODERNIZATION_RATE}%"
    else
        echo "- 主题现代化率: 0%"
    fi
} > "$REPORT_FILE"

echo "📄 详细报告已保存至: $REPORT_FILE"
