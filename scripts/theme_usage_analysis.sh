#!/bin/bash

# 主题系统使用情况分析脚本
# 用于监控新主题系统的采用情况和使用模式

echo "🎨 Flutter 主题系统使用情况分析"
echo "=================================="

# 设置颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 项目根目录
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
LIB_DIR="$PROJECT_ROOT/apps/mobile/lib"

echo -e "${BLUE}📊 分析目录: $LIB_DIR${NC}"
echo ""

# 1. 统计主题扩展使用情况
echo -e "${GREEN}1. 主题扩展使用统计${NC}"
echo "------------------------"

# 统计导入 theme_extensions.dart 的文件
THEME_IMPORTS=$(find "$LIB_DIR" -name "*.dart" -exec grep -l "theme_extensions.dart" {} \; | wc -l)
echo "✅ 导入主题扩展的文件数: $THEME_IMPORTS"

# 统计使用 context.colors 的次数
CONTEXT_COLORS=$(find "$LIB_DIR" -name "*.dart" -exec grep -o "context\.colors\." {} \; | wc -l)
echo "✅ context.colors 使用次数: $CONTEXT_COLORS"

# 统计使用 context.textStyles 的次数
CONTEXT_TEXT_STYLES=$(find "$LIB_DIR" -name "*.dart" -exec grep -o "context\.textStyles\." {} \; | wc -l)
echo "✅ context.textStyles 使用次数: $CONTEXT_TEXT_STYLES"

# 统计使用 context.themeColors 的次数
CONTEXT_THEME_COLORS=$(find "$LIB_DIR" -name "*.dart" -exec grep -o "context\.themeColors\." {} \; | wc -l)
echo "✅ context.themeColors 使用次数: $CONTEXT_THEME_COLORS"

echo ""

# 2. 统计旧方式使用情况
echo -e "${YELLOW}2. 待迁移代码统计${NC}"
echo "------------------------"

# 统计真正的硬编码颜色使用 (排除主题系统内部定义和透明色)
HARDCODED_COLORS=$(find "$LIB_DIR" -name "*.dart" -not -path "*/theme/*" -exec grep "\bColors\.[a-zA-Z]" {} \; | grep -v "Colors.transparent" | grep -v "context\." | grep -v "ThemeColors" | wc -l)
echo "⚠️  硬编码颜色使用次数: $HARDCODED_COLORS"

# 统计 withOpacity 使用
WITH_OPACITY=$(find "$LIB_DIR" -name "*.dart" -exec grep -o "\.withOpacity(" {} \; | wc -l)
echo "⚠️  withOpacity 使用次数: $WITH_OPACITY"

# 统计 Theme.of(context) 直接使用
THEME_OF_CONTEXT=$(find "$LIB_DIR" -name "*.dart" -exec grep -o "Theme\.of(context)" {} \; | wc -l)
echo "⚠️  Theme.of(context) 直接使用次数: $THEME_OF_CONTEXT"

echo ""

# 3. 性能相关统计
echo -e "${GREEN}3. 性能优化统计${NC}"
echo "------------------------"

# 统计新透明度方法使用
LIGHT_OPACITY=$(find "$LIB_DIR" -name "*.dart" -exec grep -o "\.light\b" {} \; | wc -l)
MEDIUM_OPACITY=$(find "$LIB_DIR" -name "*.dart" -exec grep -o "\.medium\b" {} \; | wc -l)
STRONG_OPACITY=$(find "$LIB_DIR" -name "*.dart" -exec grep -o "\.strong\b" {} \; | wc -l)

echo "✅ .light 使用次数: $LIGHT_OPACITY"
echo "✅ .medium 使用次数: $MEDIUM_OPACITY"
echo "✅ .strong 使用次数: $STRONG_OPACITY"

TOTAL_NEW_OPACITY=$((LIGHT_OPACITY + MEDIUM_OPACITY + STRONG_OPACITY))
echo "✅ 新透明度方法总使用次数: $TOTAL_NEW_OPACITY"

echo ""

# 4. 迁移进度计算
echo -e "${BLUE}4. 迁移进度分析${NC}"
echo "------------------------"

TOTAL_FILES=$(find "$LIB_DIR" -name "*.dart" | wc -l)
echo "📁 总 Dart 文件数: $TOTAL_FILES"

if [ $TOTAL_FILES -gt 0 ]; then
    MIGRATION_RATE=$((THEME_IMPORTS * 100 / TOTAL_FILES))
    echo "📈 主题扩展采用率: ${MIGRATION_RATE}%"
fi

if [ $WITH_OPACITY -gt 0 ] && [ $TOTAL_NEW_OPACITY -gt 0 ]; then
    OPACITY_MIGRATION_RATE=$((TOTAL_NEW_OPACITY * 100 / (WITH_OPACITY + TOTAL_NEW_OPACITY)))
    echo "📈 透明度方法迁移率: ${OPACITY_MIGRATION_RATE}%"
fi

echo ""

# 5. 问题文件识别
echo -e "${RED}5. 需要关注的文件${NC}"
echo "------------------------"

echo "🔍 包含硬编码颜色的业务文件:"
find "$LIB_DIR" -name "*.dart" -not -path "*/theme/*" -exec sh -c 'count=$(grep -o "\bColors\.[a-zA-Z][a-zA-Z0-9]*" "$1" | grep -v "Colors.transparent" | wc -l); if [ $count -gt 0 ]; then
  # 进一步检查是否是真正的硬编码颜色
  real_count=$(grep "\bColors\.[a-zA-Z]" "$1" | grep -v "context\." | grep -v "ThemeColors" | wc -l)
  if [ $real_count -gt 0 ]; then
    echo "  - $1 ($real_count 处真正硬编码)"
  fi
fi' _ {} \;

echo ""
echo "🔍 仍在使用 withOpacity 的文件:"
find "$LIB_DIR" -name "*.dart" -exec sh -c 'count=$(grep -o "\.withOpacity(" "$1" | wc -l); if [ $count -gt 0 ]; then echo "  - $1 ($count 处)"; fi' _ {} \;

echo ""

# 6. 建议和总结
echo -e "${GREEN}6. 改进建议${NC}"
echo "------------------------"

if [ $HARDCODED_COLORS -gt 10 ]; then
    echo "💡 建议优先迁移硬编码颜色，提升主题一致性"
fi

if [ $WITH_OPACITY -gt 5 ]; then
    echo "💡 建议替换 withOpacity 为新的透明度方法，提升性能"
fi

if [ $THEME_OF_CONTEXT -gt 20 ]; then
    echo "💡 建议使用 context 扩展简化主题访问代码"
fi

echo ""
echo -e "${GREEN}✅ 分析完成！${NC}"
echo "=================================="

# 生成报告文件
REPORT_FILE="$PROJECT_ROOT/theme_usage_report_$(date +%Y%m%d_%H%M%S).txt"
{
    echo "Flutter 主题系统使用情况报告"
    echo "生成时间: $(date)"
    echo "=================================="
    echo ""
    echo "主题扩展使用统计:"
    echo "- 导入主题扩展的文件数: $THEME_IMPORTS"
    echo "- context.colors 使用次数: $CONTEXT_COLORS"
    echo "- context.textStyles 使用次数: $CONTEXT_TEXT_STYLES"
    echo "- context.themeColors 使用次数: $CONTEXT_THEME_COLORS"
    echo ""
    echo "待迁移代码统计:"
    echo "- 硬编码颜色使用次数: $HARDCODED_COLORS"
    echo "- withOpacity 使用次数: $WITH_OPACITY"
    echo "- Theme.of(context) 直接使用次数: $THEME_OF_CONTEXT"
    echo ""
    echo "性能优化统计:"
    echo "- 新透明度方法总使用次数: $TOTAL_NEW_OPACITY"
    echo ""
    if [ $TOTAL_FILES -gt 0 ]; then
        echo "迁移进度:"
        echo "- 主题扩展采用率: ${MIGRATION_RATE}%"
    fi
} > "$REPORT_FILE"

echo "📄 详细报告已保存至: $REPORT_FILE"
