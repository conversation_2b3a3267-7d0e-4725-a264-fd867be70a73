#!/bin/bash

# Flutter 代码生成自动修复脚本
# 用于解决循环依赖和构建阶段冲突问题
# 
# 使用方法:
#   chmod +x scripts/fix_codegen.sh
#   ./scripts/fix_codegen.sh

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

log_step() {
    echo -e "${BLUE}🔨 $1${NC}"
}

# 检查必要的文件和目录
check_prerequisites() {
    log_info "检查项目结构..."
    
    if [ ! -f "pubspec.yaml" ]; then
        log_error "未找到 pubspec.yaml，请在 Flutter 项目根目录运行此脚本"
        exit 1
    fi
    
    if [ ! -d "lib" ]; then
        log_error "未找到 lib 目录"
        exit 1
    fi
    
    log_success "项目结构检查通过"
}

# 备份重要文件
backup_files() {
    log_info "备份重要文件..."
    
    local backup_dir=".backup_$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$backup_dir"
    
    if [ -f "lib/core/di/di.dart" ]; then
        cp "lib/core/di/di.dart" "$backup_dir/di.dart.backup"
        log_success "已备份 di.dart 到 $backup_dir/"
    fi
    
    if [ -f "build.yaml" ]; then
        cp "build.yaml" "$backup_dir/build.yaml.backup"
        log_success "已备份 build.yaml 到 $backup_dir/"
    fi
}

# 检测并修复循环依赖
fix_circular_dependency() {
    log_info "检测循环依赖..."
    
    local di_file="lib/core/di/di.dart"
    local config_file="lib/core/di/di.config.dart"
    
    if [ -f "$di_file" ]; then
        # 检查是否存在循环依赖
        if grep -q "import.*di\.config\.dart" "$di_file" && [ ! -f "$config_file" ]; then
            log_warning "检测到循环依赖，正在修复..."
            
            # 注释掉循环导入
            sed -i.bak "s/^import.*di\.config\.dart/\/\/ &/" "$di_file"
            
            # 注释掉使用生成函数的代码
            sed -i.bak "s/.*\$initGetIt.*/  \/\/ 临时注释，将在代码生成后恢复/" "$di_file"
            
            log_success "已临时修复循环依赖"
        else
            log_info "未检测到循环依赖问题"
        fi
    else
        log_warning "未找到 di.dart 文件"
    fi
}

# 清理构建缓存
clean_build_cache() {
    log_step "清理构建缓存..."
    
    # 清理 Flutter 缓存
    if command -v flutter &> /dev/null; then
        flutter clean
        log_success "Flutter 缓存已清理"
    fi
    
    # 清理 build_runner 缓存
    if [ -d ".dart_tool" ]; then
        rm -rf .dart_tool
        log_success ".dart_tool 目录已清理"
    fi
    
    # 清理生成文件
    if command -v dart &> /dev/null; then
        dart run build_runner clean 2>/dev/null || true
        log_success "build_runner 缓存已清理"
    fi
}

# 重新安装依赖
reinstall_dependencies() {
    log_step "重新安装依赖..."
    
    if command -v flutter &> /dev/null; then
        flutter pub get
        log_success "依赖安装完成"
    else
        log_error "未找到 Flutter 命令"
        exit 1
    fi
}

# 分步生成代码
generate_code_step_by_step() {
    log_step "开始分步代码生成..."
    
    # 步骤 1: 生成 Freezed 文件
    log_step "步骤 1/5: 生成 Freezed 文件..."
    if dart run build_runner build --build-filter="**/*.freezed.dart" --delete-conflicting-outputs; then
        log_success "Freezed 文件生成完成"
    else
        log_warning "Freezed 文件生成失败，继续下一步..."
    fi
    
    # 步骤 2: 生成 JSON 序列化和 Retrofit 文件
    log_step "步骤 2/5: 生成 JSON 序列化和 Retrofit 文件..."
    if dart run build_runner build --build-filter="**/*.g.dart" --delete-conflicting-outputs; then
        log_success "JSON 序列化和 Retrofit 文件生成完成"
    else
        log_warning "JSON 序列化和 Retrofit 文件生成失败，继续下一步..."
    fi
    
    # 步骤 3: 恢复循环导入
    restore_circular_dependency
    
    # 步骤 4: 生成 Injectable 配置
    log_step "步骤 4/5: 生成 Injectable 配置..."
    if dart run build_runner build --build-filter="**/*.config.dart" --delete-conflicting-outputs; then
        log_success "Injectable 配置生成完成"
    else
        log_warning "Injectable 配置生成失败，继续下一步..."
    fi
    
    # 步骤 5: 生成 Assets 和 Fonts 文件
    log_step "步骤 5/5: 生成 Assets 和 Fonts 文件..."
    if dart run build_runner build --build-filter="**/assets.gen.dart" --build-filter="**/fonts.gen.dart" --delete-conflicting-outputs; then
        log_success "Assets 和 Fonts 文件生成完成"
    else
        log_warning "Assets 和 Fonts 文件生成失败"
    fi
}

# 恢复循环依赖
restore_circular_dependency() {
    log_step "步骤 3/5: 恢复循环导入..."
    
    local di_file="lib/core/di/di.dart"
    
    if [ -f "$di_file" ]; then
        # 取消注释循环导入
        sed -i.bak "s/^\/\/ import.*di\.config\.dart/import 'di.config.dart';/" "$di_file"
        
        # 恢复使用生成函数的代码
        sed -i.bak "s/.*临时注释，将在代码生成后恢复.*/Future<void> configureDependencies() async => \$initGetIt(getIt);/" "$di_file"
        
        log_success "循环导入已恢复"
    fi
}

# 验证生成结果
verify_generation() {
    log_info "验证代码生成结果..."
    
    local success=true
    
    # 检查关键生成文件
    local files=(
        "lib/core/di/di.config.dart"
        "lib/generated/assets.gen.dart"
        "lib/generated/fonts.gen.dart"
    )
    
    for file in "${files[@]}"; do
        if [ -f "$file" ]; then
            log_success "✓ $file"
        else
            log_warning "✗ $file (可能不需要或生成失败)"
        fi
    done
    
    # 检查编译状态
    log_info "检查编译状态..."
    if flutter analyze --no-fatal-infos > /dev/null 2>&1; then
        log_success "代码分析通过"
    else
        log_warning "代码分析发现问题，请手动检查"
        success=false
    fi
    
    return $success
}

# 显示使用建议
show_usage_tips() {
    log_info "使用建议："
    echo ""
    echo "1. 开发时使用监听模式："
    echo "   dart run build_runner watch --delete-conflicting-outputs"
    echo ""
    echo "2. 如果再次遇到问题，可以重新运行此脚本："
    echo "   ./scripts/fix_codegen.sh"
    echo ""
    echo "3. 查看详细文档："
    echo "   docs/CODE_GENERATION_TROUBLESHOOTING.md"
    echo ""
}

# 主函数
main() {
    echo "🚀 Flutter 代码生成自动修复脚本"
    echo "=================================="
    echo ""
    
    check_prerequisites
    backup_files
    fix_circular_dependency
    clean_build_cache
    reinstall_dependencies
    generate_code_step_by_step
    
    echo ""
    echo "=================================="
    
    if verify_generation; then
        log_success "🎉 代码生成修复完成！"
        show_usage_tips
    else
        log_warning "⚠️  代码生成部分完成，请检查警告信息"
        echo ""
        echo "如果问题仍然存在，请查看详细文档："
        echo "docs/CODE_GENERATION_TROUBLESHOOTING.md"
    fi
}

# 运行主函数
main "$@"
