import 'package:fpdart/fpdart.dart';
import 'package:equatable/equatable.dart';

import '../../../../core/error/failure.dart';
import '../../../../core/usecase/usecase.dart';
import '../../../../core/logging/app_logger.dart';
import '../repositories/{{repository.snakeCase()}}_repository.dart';
import '../entities/{{entity.snakeCase()}}.dart';

/// UseCase for {{name.titleCase()}} operation
///
/// This UseCase handles the business logic for {{name.titleCase()}}.
/// It follows the UseCase pattern from Clean Architecture.
///
/// Generated by Mason 🧱
class {{name.pascalCase()}}UseCase implements UseCase<{{entity.pascalCase()}}, {{name.pascalCase()}}Params> {
  final {{repository.pascalCase()}}Repository repository;

  const {{name.pascalCase()}}UseCase({
    required this.repository,
  });

  @override
  Future<Either<Failure, {{entity.pascalCase()}}>> call({{name.pascalCase()}}Params params) async {
    AppLogger.debug('{{name.pascalCase()}}UseCase: Executing with params: $params');

    try {
      final result = await repository.create{{entity.pascalCase()}}(params.{{entity.camelCase()}});
      return result.fold(
        (failure) {
          AppLogger.error('{{name.pascalCase()}}UseCase: Operation failed', failure.message);
          return Left(failure);
        },
        ({{entity.camelCase()}}) {
          AppLogger.info('{{name.pascalCase()}}UseCase: Operation completed successfully');
          return Right({{entity.camelCase()}});
        },
      );
    } catch (e) {
      AppLogger.error('{{name.pascalCase()}}UseCase: Unexpected error', e.toString());
      return Left(UnknownFailure(message: 'Unexpected error: ${e.toString()}'));
    }
  }
}

/// Parameters for {{name.pascalCase()}}UseCase
class {{name.pascalCase()}}Params extends Equatable {
  final {{entity.pascalCase()}} {{entity.camelCase()}};

  const {{name.pascalCase()}}Params({
    required this.{{entity.camelCase()}},
  });

  @override
  List<Object?> get props => [{{entity.camelCase()}}];

  @override
  String toString() {
    return '{{name.pascalCase()}}Params({{entity.camelCase()}}: ${{entity.camelCase()}})';
  }
}