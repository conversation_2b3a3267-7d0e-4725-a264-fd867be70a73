import 'package:fpdart/fpdart.dart';

import '../../../../core/error/failure.dart';
import '../../../../core/error/exceptions.dart';
import '../../../../core/network/network_info.dart';
import '../../../../core/logging/app_logger.dart';
import '../../domain/repositories/{{name.snakeCase()}}_repository.dart';
import '../../domain/entities/{{model.snakeCase()}}.dart';
import '../datasources/{{model.snakeCase()}}_remote_data_source.dart';
import '../datasources/{{model.snakeCase()}}_local_data_source.dart';
import '../models/{{model.snakeCase()}}_model.dart';

/// Implementation of {{name.pascalCase()}}Repository
///
/// This repository implementation handles data operations for {{model.titleCase()}}.
/// It follows the Repository pattern with proper error handling using Either.
///
/// Features:
/// - Network connectivity checking
/// - Local caching with fallback
/// - Comprehensive error handling
/// - Logging for debugging
///
/// Generated by Mason 🧱
class {{name.pascalCase()}}RepositoryImpl implements {{name.pascalCase()}}Repository {
  final {{model.pascalCase()}}RemoteDataSource remoteDataSource;
  final {{model.pascalCase()}}LocalDataSource localDataSource;
  final NetworkInfo networkInfo;

  const {{name.pascalCase()}}RepositoryImpl({
    required this.remoteDataSource,
    required this.localDataSource,
    required this.networkInfo,
  });

  @override
  Future<Either<Failure, {{model.pascalCase()}}>> get{{model.pascalCase()}}ById(String id) async {
    AppLogger.debug('{{name.pascalCase()}}Repository: Getting {{model.titleCase()}} by ID: $id');

    if (await networkInfo.isConnected) {
      try {
        final remoteModel = await remoteDataSource.get{{model.pascalCase()}}(id);
        // Cache the result locally
        await localDataSource.save{{model.pascalCase()}}(remoteModel);
        AppLogger.info('{{name.pascalCase()}}Repository: {{model.titleCase()}} fetched from remote and cached');
        return Right(remoteModel.toEntity());
      } on ServerException catch (e) {
        AppLogger.error('{{name.pascalCase()}}Repository: Server error while fetching {{model.titleCase()}}', e.message);
        return Left(ServerFailure(message: e.message));
      } on NetworkException catch (e) {
        AppLogger.error('{{name.pascalCase()}}Repository: Network error while fetching {{model.titleCase()}}', e.message);
        return Left(NetworkFailure(message: e.message));
      } catch (e) {
        AppLogger.error('{{name.pascalCase()}}Repository: Unexpected error while fetching {{model.titleCase()}}', e.toString());
        return Left(UnknownFailure(message: 'Unexpected error: ${e.toString()}'));
      }
    } else {
      try {
        final localModel = await localDataSource.get{{model.pascalCase()}}(id);
        if (localModel != null) {
          AppLogger.info('{{name.pascalCase()}}Repository: {{model.titleCase()}} fetched from local cache');
          return Right(localModel.toEntity());
        } else {
          AppLogger.warning('{{name.pascalCase()}}Repository: {{model.titleCase()}} not found in local cache');
          return const Left(CacheFailure(message: '{{model.titleCase()}} not found in cache'));
        }
      } on CacheException catch (e) {
        AppLogger.error('{{name.pascalCase()}}Repository: Cache error while fetching {{model.titleCase()}}', e.message);
        return Left(CacheFailure(message: e.message));
      } catch (e) {
        AppLogger.error('{{name.pascalCase()}}Repository: Unexpected cache error', e.toString());
        return Left(CacheFailure(message: 'Cache error: ${e.toString()}'));
      }
    }
  }

  @override
  Future<Either<Failure, List<{{model.pascalCase()}}>>> getAll{{model.pascalCase()}}s() async {
    AppLogger.debug('{{name.pascalCase()}}Repository: Getting all {{model.titleCase()}}s');

    if (await networkInfo.isConnected) {
      try {
        final models = await remoteDataSource.getAll{{model.pascalCase()}}s();
        // Update local cache
        await localDataSource.saveAll{{model.pascalCase()}}s(models);
        AppLogger.info('{{name.pascalCase()}}Repository: ${models.length} {{model.titleCase()}}s fetched from remote and cached');
        return Right(models.map((model) => model.toEntity()).toList());
      } on ServerException catch (e) {
        AppLogger.error('{{name.pascalCase()}}Repository: Server error while fetching all {{model.titleCase()}}s', e.message);
        // Try to get from cache as fallback
        return _getAllFromCache();
      } on NetworkException catch (e) {
        AppLogger.error('{{name.pascalCase()}}Repository: Network error while fetching all {{model.titleCase()}}s', e.message);
        return _getAllFromCache();
      } catch (e) {
        AppLogger.error('{{name.pascalCase()}}Repository: Unexpected error while fetching all {{model.titleCase()}}s', e.toString());
        return _getAllFromCache();
      }
    } else {
      return _getAllFromCache();
    }
  }

  Future<Either<Failure, List<{{model.pascalCase()}}>>> _getAllFromCache() async {
    try {
      final localModels = await localDataSource.getAll{{model.pascalCase()}}s();
      AppLogger.info('{{name.pascalCase()}}Repository: ${localModels.length} {{model.titleCase()}}s fetched from local cache');
      return Right(localModels.map((model) => model.toEntity()).toList());
    } on CacheException catch (e) {
      AppLogger.error('{{name.pascalCase()}}Repository: Cache error while fetching all {{model.titleCase()}}s', e.message);
      return Left(CacheFailure(message: e.message));
    } catch (e) {
      AppLogger.error('{{name.pascalCase()}}Repository: Unexpected cache error', e.toString());
      return Left(CacheFailure(message: 'Cache error: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, {{model.pascalCase()}}>> create{{model.pascalCase()}}({{model.pascalCase()}} {{model.camelCase()}}) async {
    AppLogger.debug('{{name.pascalCase()}}Repository: Creating {{model.titleCase()}}');

    if (await networkInfo.isConnected) {
      try {
        final model = {{model.pascalCase()}}Model.fromEntity({{model.camelCase()}});
        final createdModel = await remoteDataSource.create{{model.pascalCase()}}(model);
        // Save to local cache
        await localDataSource.save{{model.pascalCase()}}(createdModel);
        AppLogger.info('{{name.pascalCase()}}Repository: {{model.titleCase()}} created successfully');
        return Right(createdModel.toEntity());
      } on ServerException catch (e) {
        AppLogger.error('{{name.pascalCase()}}Repository: Server error while creating {{model.titleCase()}}', e.message);
        return Left(ServerFailure(message: e.message));
      } on NetworkException catch (e) {
        AppLogger.error('{{name.pascalCase()}}Repository: Network error while creating {{model.titleCase()}}', e.message);
        return Left(NetworkFailure(message: e.message));
      } catch (e) {
        AppLogger.error('{{name.pascalCase()}}Repository: Unexpected error while creating {{model.titleCase()}}', e.toString());
        return Left(UnknownFailure(message: 'Unexpected error: ${e.toString()}'));
      }
    } else {
      AppLogger.warning('{{name.pascalCase()}}Repository: Cannot create {{model.titleCase()}} - no network connection');
      return const Left(NetworkFailure(message: 'No network connection available'));
    }
  }

  @override
  Future<Either<Failure, {{model.pascalCase()}}>> update{{model.pascalCase()}}({{model.pascalCase()}} {{model.camelCase()}}) async {
    AppLogger.debug('{{name.pascalCase()}}Repository: Updating {{model.titleCase()}}');

    if (await networkInfo.isConnected) {
      try {
        final model = {{model.pascalCase()}}Model.fromEntity({{model.camelCase()}});
        final updatedModel = await remoteDataSource.update{{model.pascalCase()}}(model);
        // Update local cache
        await localDataSource.save{{model.pascalCase()}}(updatedModel);
        AppLogger.info('{{name.pascalCase()}}Repository: {{model.titleCase()}} updated successfully');
        return Right(updatedModel.toEntity());
      } on ServerException catch (e) {
        AppLogger.error('{{name.pascalCase()}}Repository: Server error while updating {{model.titleCase()}}', e.message);
        return Left(ServerFailure(message: e.message));
      } on NetworkException catch (e) {
        AppLogger.error('{{name.pascalCase()}}Repository: Network error while updating {{model.titleCase()}}', e.message);
        return Left(NetworkFailure(message: e.message));
      } catch (e) {
        AppLogger.error('{{name.pascalCase()}}Repository: Unexpected error while updating {{model.titleCase()}}', e.toString());
        return Left(UnknownFailure(message: 'Unexpected error: ${e.toString()}'));
      }
    } else {
      AppLogger.warning('{{name.pascalCase()}}Repository: Cannot update {{model.titleCase()}} - no network connection');
      return const Left(NetworkFailure(message: 'No network connection available'));
    }
  }

  @override
  Future<Either<Failure, void>> delete{{model.pascalCase()}}(String id) async {
    AppLogger.debug('{{name.pascalCase()}}Repository: Deleting {{model.titleCase()}} with ID: $id');

    if (await networkInfo.isConnected) {
      try {
        await remoteDataSource.delete{{model.pascalCase()}}(id);
        // Remove from local cache
        await localDataSource.delete{{model.pascalCase()}}(id);
        AppLogger.info('{{name.pascalCase()}}Repository: {{model.titleCase()}} deleted successfully');
        return const Right(null);
      } on ServerException catch (e) {
        AppLogger.error('{{name.pascalCase()}}Repository: Server error while deleting {{model.titleCase()}}', e.message);
        return Left(ServerFailure(message: e.message));
      } on NetworkException catch (e) {
        AppLogger.error('{{name.pascalCase()}}Repository: Network error while deleting {{model.titleCase()}}', e.message);
        return Left(NetworkFailure(message: e.message));
      } catch (e) {
        AppLogger.error('{{name.pascalCase()}}Repository: Unexpected error while deleting {{model.titleCase()}}', e.toString());
        return Left(UnknownFailure(message: 'Unexpected error: ${e.toString()}'));
      }
    } else {
      AppLogger.warning('{{name.pascalCase()}}Repository: Cannot delete {{model.titleCase()}} - no network connection');
      return const Left(NetworkFailure(message: 'No network connection available'));
    }
  }

  @override
  Future<Either<Failure, List<{{model.pascalCase()}}>>> search{{model.pascalCase()}}s(String query) async {
    AppLogger.debug('{{name.pascalCase()}}Repository: Searching {{model.titleCase()}}s with query: $query');

    if (await networkInfo.isConnected) {
      try {
        final models = await remoteDataSource.search{{model.pascalCase()}}s(query);
        AppLogger.info('{{name.pascalCase()}}Repository: Found ${models.length} {{model.titleCase()}}s matching query');
        return Right(models.map((model) => model.toEntity()).toList());
      } on ServerException catch (e) {
        AppLogger.error('{{name.pascalCase()}}Repository: Server error while searching {{model.titleCase()}}s', e.message);
        return Left(ServerFailure(message: e.message));
      } on NetworkException catch (e) {
        AppLogger.error('{{name.pascalCase()}}Repository: Network error while searching {{model.titleCase()}}s', e.message);
        return Left(NetworkFailure(message: e.message));
      } catch (e) {
        AppLogger.error('{{name.pascalCase()}}Repository: Unexpected error while searching {{model.titleCase()}}s', e.toString());
        return Left(UnknownFailure(message: 'Unexpected error: ${e.toString()}'));
      }
    } else {
      AppLogger.warning('{{name.pascalCase()}}Repository: Cannot search {{model.titleCase()}}s - no network connection');
      return const Left(NetworkFailure(message: 'No network connection available'));
    }
  }

  @override
  Future<Either<Failure, List<{{model.pascalCase()}}>>> getPaginated{{model.pascalCase()}}s({
    int page = 1,
    int limit = 20,
  }) async {
    AppLogger.debug('{{name.pascalCase()}}Repository: Getting paginated {{model.titleCase()}}s (page: $page, limit: $limit)');

    if (await networkInfo.isConnected) {
      try {
        final models = await remoteDataSource.getPaginated{{model.pascalCase()}}s(
          page: page,
          limit: limit,
        );
        AppLogger.info('{{name.pascalCase()}}Repository: Fetched ${models.length} {{model.titleCase()}}s for page $page');
        return Right(models.map((model) => model.toEntity()).toList());
      } on ServerException catch (e) {
        AppLogger.error('{{name.pascalCase()}}Repository: Server error while getting paginated {{model.titleCase()}}s', e.message);
        return Left(ServerFailure(message: e.message));
      } on NetworkException catch (e) {
        AppLogger.error('{{name.pascalCase()}}Repository: Network error while getting paginated {{model.titleCase()}}s', e.message);
        return Left(NetworkFailure(message: e.message));
      } catch (e) {
        AppLogger.error('{{name.pascalCase()}}Repository: Unexpected error while getting paginated {{model.titleCase()}}s', e.toString());
        return Left(UnknownFailure(message: 'Unexpected error: ${e.toString()}'));
      }
    } else {
      AppLogger.warning('{{name.pascalCase()}}Repository: Cannot get paginated {{model.titleCase()}}s - no network connection');
      return const Left(NetworkFailure(message: 'No network connection available'));
    }
  }
}