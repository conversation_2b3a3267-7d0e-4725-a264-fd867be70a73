import 'package:fpdart/fpdart.dart';

import '../../../../core/error/failure.dart';
import '../entities/{{model.snakeCase()}}.dart';

/// Repository interface for {{model.titleCase()}} operations
///
/// This repository defines the contract for {{model.titleCase()}} data operations.
/// It follows the Repository pattern from Clean Architecture.
///
/// All methods return Either<Failure, T> for proper error handling.
/// Generated by Mason 🧱
abstract class {{name.pascalCase()}}Repository {
  /// Retrieves a {{model.titleCase()}} by its ID
  ///
  /// Returns [Right({{model.pascalCase()}})] if found, [Left(Failure)] if not found or error occurs
  Future<Either<Failure, {{model.pascalCase()}}>> get{{model.pascalCase()}}ById(String id);

  /// Retrieves all {{model.titleCase()}}s
  ///
  /// Returns [Right(List<{{model.pascalCase()}}>)] if successful, [Left(Failure)] if error occurs
  Future<Either<Failure, List<{{model.pascalCase()}}>>> getAll{{model.pascalCase()}}s();

  /// Creates a new {{model.titleCase()}}
  ///
  /// Returns [Right({{model.pascalCase()}})] if created successfully, [Left(Failure)] if error occurs
  Future<Either<Failure, {{model.pascalCase()}}>> create{{model.pascalCase()}}({{model.pascalCase()}} {{model.camelCase()}});

  /// Updates an existing {{model.titleCase()}}
  ///
  /// Returns [Right({{model.pascalCase()}})] if updated successfully, [Left(Failure)] if error occurs
  Future<Either<Failure, {{model.pascalCase()}}>> update{{model.pascalCase()}}({{model.pascalCase()}} {{model.camelCase()}});

  /// Deletes a {{model.titleCase()}} by its ID
  ///
  /// Returns [Right(void)] if deleted successfully, [Left(Failure)] if error occurs
  Future<Either<Failure, void>> delete{{model.pascalCase()}}(String id);

  /// Searches {{model.titleCase()}}s by query
  ///
  /// Returns [Right(List<{{model.pascalCase()}}>)] if successful, [Left(Failure)] if error occurs
  Future<Either<Failure, List<{{model.pascalCase()}}>>> search{{model.pascalCase()}}s(String query);

  /// Gets paginated {{model.titleCase()}}s
  ///
  /// Returns [Right(List<{{model.pascalCase()}}>)] if successful, [Left(Failure)] if error occurs
  Future<Either<Failure, List<{{model.pascalCase()}}>>> getPaginated{{model.pascalCase()}}s({
    int page = 1,
    int limit = 20,
  });
}