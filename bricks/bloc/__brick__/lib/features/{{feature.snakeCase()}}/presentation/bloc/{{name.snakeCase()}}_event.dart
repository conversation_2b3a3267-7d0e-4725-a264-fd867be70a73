part of '{{name.snakeCase()}}_bloc.dart';

/// Events for {{name.pascalCase()}}Bloc
///
/// All events that can be dispatched to the {{name.pascalCase()}}Bloc
/// Generated by <PERSON> 🧱
abstract class {{name.pascalCase()}}Event extends Equatable {
  const {{name.pascalCase()}}Event();

  @override
  List<Object?> get props => [];
}

/// Event to initialize the BLoC
class {{name.pascalCase()}}Started extends {{name.pascalCase()}}Event {
  const {{name.pascalCase()}}Started();
}

/// Event to load {{entity.titleCase()}} data
class {{name.pascalCase()}}Load{{entity.pascalCase()}} extends {{name.pascalCase()}}Event {
  const {{name.pascalCase()}}Load{{entity.pascalCase()}}();
}

/// Event to create a new {{entity.titleCase()}}
class {{name.pascalCase()}}Create{{entity.pascalCase()}} extends {{name.pascalCase()}}Event {
  final {{entity.pascalCase()}} {{entity.camelCase()}};

  const {{name.pascalCase()}}Create{{entity.pascalCase()}}({
    required this.{{entity.camelCase()}},
  });

  @override
  List<Object?> get props => [{{entity.camelCase()}}];
}

/// Event to update an existing {{entity.titleCase()}}
class {{name.pascalCase()}}Update{{entity.pascalCase()}} extends {{name.pascalCase()}}Event {
  final {{entity.pascalCase()}} {{entity.camelCase()}};

  const {{name.pascalCase()}}Update{{entity.pascalCase()}}({
    required this.{{entity.camelCase()}},
  });

  @override
  List<Object?> get props => [{{entity.camelCase()}}];
}

/// Event to delete a {{entity.titleCase()}}
class {{name.pascalCase()}}Delete{{entity.pascalCase()}} extends {{name.pascalCase()}}Event {
  final String id;

  const {{name.pascalCase()}}Delete{{entity.pascalCase()}}({
    required this.id,
  });

  @override
  List<Object?> get props => [id];
}