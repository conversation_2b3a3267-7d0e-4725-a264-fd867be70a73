import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:fpdart/fpdart.dart';

import '../../../../core/error/failure.dart';
import '../../../../core/usecase/usecase.dart';
import '../../../../core/logging/app_logger.dart';
import '../../domain/entities/{{entity.snakeCase()}}.dart';
import '../../domain/usecases/get_{{entity.snakeCase()}}_usecase.dart';
import '../../domain/usecases/create_{{entity.snakeCase()}}_usecase.dart';
import '../../domain/usecases/update_{{entity.snakeCase()}}_usecase.dart';
import '../../domain/usecases/delete_{{entity.snakeCase()}}_usecase.dart';

part '{{name.snakeCase()}}_event.dart';
part '{{name.snakeCase()}}_state.dart';

/// BLoC for managing {{entity.titleCase()}} operations
///
/// This BLoC handles all {{entity.titleCase()}} related operations including:
/// - Loading {{entity.titleCase()}} data
/// - Creating new {{entity.titleCase()}}
/// - Updating existing {{entity.titleCase()}}
/// - Deleting {{entity.titleCase()}}
///
/// Generated by Mason 🧱
class {{name.pascalCase()}}Bloc extends Bloc<{{name.pascalCase()}}Event, {{name.pascalCase()}}State> {
  final Get{{entity.pascalCase()}}UseCase get{{entity.pascalCase()}}UseCase;
  final Create{{entity.pascalCase()}}UseCase create{{entity.pascalCase()}}UseCase;
  final Update{{entity.pascalCase()}}UseCase update{{entity.pascalCase()}}UseCase;
  final Delete{{entity.pascalCase()}}UseCase delete{{entity.pascalCase()}}UseCase;

  {{name.pascalCase()}}Bloc({
    required this.get{{entity.pascalCase()}}UseCase,
    required this.create{{entity.pascalCase()}}UseCase,
    required this.update{{entity.pascalCase()}}UseCase,
    required this.delete{{entity.pascalCase()}}UseCase,
  }) : super(const {{name.pascalCase()}}State()) {
    on<{{name.pascalCase()}}Started>(_on{{name.pascalCase()}}Started);
    on<{{name.pascalCase()}}Load{{entity.pascalCase()}}>(_on{{name.pascalCase()}}Load{{entity.pascalCase()}});
    on<{{name.pascalCase()}}Create{{entity.pascalCase()}}>(_on{{name.pascalCase()}}Create{{entity.pascalCase()}});
    on<{{name.pascalCase()}}Update{{entity.pascalCase()}}>(_on{{name.pascalCase()}}Update{{entity.pascalCase()}});
    on<{{name.pascalCase()}}Delete{{entity.pascalCase()}}>(_on{{name.pascalCase()}}Delete{{entity.pascalCase()}});
  }

  Future<void> _on{{name.pascalCase()}}Started(
    {{name.pascalCase()}}Started event,
    Emitter<{{name.pascalCase()}}State> emit,
  ) async {
    AppLogger.debug('{{name.pascalCase()}}Bloc: Started event received');
    emit(state.copyWith(status: {{name.pascalCase()}}Status.initial));
  }

  Future<void> _on{{name.pascalCase()}}Load{{entity.pascalCase()}}(
    {{name.pascalCase()}}Load{{entity.pascalCase()}} event,
    Emitter<{{name.pascalCase()}}State> emit,
  ) async {
    AppLogger.debug('{{name.pascalCase()}}Bloc: Loading {{entity.titleCase()}}');
    emit(state.copyWith(status: {{name.pascalCase()}}Status.loading));

    final result = await get{{entity.pascalCase()}}UseCase(const NoParams());
    result.fold(
      (failure) {
        AppLogger.error('{{name.pascalCase()}}Bloc: Failed to load {{entity.titleCase()}}', failure.message);
        emit(state.copyWith(
          status: {{name.pascalCase()}}Status.failure,
          error: failure.message,
        ));
      },
      ({{entity.camelCase()}}) {
        AppLogger.info('{{name.pascalCase()}}Bloc: {{entity.titleCase()}} loaded successfully');
        emit(state.copyWith(
          status: {{name.pascalCase()}}Status.success,
          {{entity.camelCase()}}: {{entity.camelCase()}},
          error: null,
        ));
      },
    );
  }

  Future<void> _on{{name.pascalCase()}}Create{{entity.pascalCase()}}(
    {{name.pascalCase()}}Create{{entity.pascalCase()}} event,
    Emitter<{{name.pascalCase()}}State> emit,
  ) async {
    AppLogger.debug('{{name.pascalCase()}}Bloc: Creating {{entity.titleCase()}}');
    emit(state.copyWith(status: {{name.pascalCase()}}Status.loading));

    final result = await create{{entity.pascalCase()}}UseCase(
      Create{{entity.pascalCase()}}Params({{entity.camelCase()}}: event.{{entity.camelCase()}}),
    );
    result.fold(
      (failure) {
        AppLogger.error('{{name.pascalCase()}}Bloc: Failed to create {{entity.titleCase()}}', failure.message);
        emit(state.copyWith(
          status: {{name.pascalCase()}}Status.failure,
          error: failure.message,
        ));
      },
      ({{entity.camelCase()}}) {
        AppLogger.info('{{name.pascalCase()}}Bloc: {{entity.titleCase()}} created successfully');
        emit(state.copyWith(
          status: {{name.pascalCase()}}Status.success,
          {{entity.camelCase()}}: {{entity.camelCase()}},
          error: null,
        ));
      },
    );
  }

  Future<void> _on{{name.pascalCase()}}Update{{entity.pascalCase()}}(
    {{name.pascalCase()}}Update{{entity.pascalCase()}} event,
    Emitter<{{name.pascalCase()}}State> emit,
  ) async {
    AppLogger.debug('{{name.pascalCase()}}Bloc: Updating {{entity.titleCase()}}');
    emit(state.copyWith(status: {{name.pascalCase()}}Status.loading));

    final result = await update{{entity.pascalCase()}}UseCase(
      Update{{entity.pascalCase()}}Params({{entity.camelCase()}}: event.{{entity.camelCase()}}),
    );
    result.fold(
      (failure) {
        AppLogger.error('{{name.pascalCase()}}Bloc: Failed to update {{entity.titleCase()}}', failure.message);
        emit(state.copyWith(
          status: {{name.pascalCase()}}Status.failure,
          error: failure.message,
        ));
      },
      ({{entity.camelCase()}}) {
        AppLogger.info('{{name.pascalCase()}}Bloc: {{entity.titleCase()}} updated successfully');
        emit(state.copyWith(
          status: {{name.pascalCase()}}Status.success,
          {{entity.camelCase()}}: {{entity.camelCase()}},
          error: null,
        ));
      },
    );
  }

  Future<void> _on{{name.pascalCase()}}Delete{{entity.pascalCase()}}(
    {{name.pascalCase()}}Delete{{entity.pascalCase()}} event,
    Emitter<{{name.pascalCase()}}State> emit,
  ) async {
    AppLogger.debug('{{name.pascalCase()}}Bloc: Deleting {{entity.titleCase()}}');
    emit(state.copyWith(status: {{name.pascalCase()}}Status.loading));

    final result = await delete{{entity.pascalCase()}}UseCase(
      Delete{{entity.pascalCase()}}Params(id: event.id),
    );
    result.fold(
      (failure) {
        AppLogger.error('{{name.pascalCase()}}Bloc: Failed to delete {{entity.titleCase()}}', failure.message);
        emit(state.copyWith(
          status: {{name.pascalCase()}}Status.failure,
          error: failure.message,
        ));
      },
      (_) {
        AppLogger.info('{{name.pascalCase()}}Bloc: {{entity.titleCase()}} deleted successfully');
        emit(state.copyWith(
          status: {{name.pascalCase()}}Status.success,
          {{entity.camelCase()}}: null,
          error: null,
        ));
      },
    );
  }
}