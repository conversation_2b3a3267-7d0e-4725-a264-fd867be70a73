part of '{{name.snakeCase()}}_bloc.dart';

/// Status enum for {{name.pascalCase()}}State
enum {{name.pascalCase()}}Status {
  initial,
  loading,
  success,
  failure,
}

/// State for {{name.pascalCase()}}Bloc
///
/// Represents the current state of {{entity.titleCase()}} operations
/// Generated by Mason 🧱
class {{name.pascalCase()}}State extends Equatable {
  final {{name.pascalCase()}}Status status;
  final {{entity.pascalCase()}}? {{entity.camelCase()}};
  final List<{{entity.pascalCase()}}>? {{entity.camelCase()}}List;
  final String? error;
  final DateTime? lastUpdated;

  const {{name.pascalCase()}}State({
    this.status = {{name.pascalCase()}}Status.initial,
    this.{{entity.camelCase()}},
    this.{{entity.camelCase()}}List,
    this.error,
    this.lastUpdated,
  });

  {{name.pascalCase()}}State copyWith({
    {{name.pascalCase()}}Status? status,
    {{entity.pascalCase()}}? {{entity.camelCase()}},
    List<{{entity.pascalCase()}}>? {{entity.camelCase()}}List,
    String? error,
    DateTime? lastUpdated,
    bool clearError = false,
    bool clear{{entity.pascalCase()}} = false,
    bool clear{{entity.pascalCase()}}List = false,
  }) {
    return {{name.pascalCase()}}State(
      status: status ?? this.status,
      {{entity.camelCase()}}: clear{{entity.pascalCase()}} ? null : ({{entity.camelCase()}} ?? this.{{entity.camelCase()}}),
      {{entity.camelCase()}}List: clear{{entity.pascalCase()}}List ? null : ({{entity.camelCase()}}List ?? this.{{entity.camelCase()}}List),
      error: clearError ? null : (error ?? this.error),
      lastUpdated: lastUpdated ?? DateTime.now(),
    );
  }

  @override
  List<Object?> get props => [
    status,
    {{entity.camelCase()}},
    {{entity.camelCase()}}List,
    error,
    lastUpdated,
  ];

  // Convenience getters
  bool get isInitial => status == {{name.pascalCase()}}Status.initial;
  bool get isLoading => status == {{name.pascalCase()}}Status.loading;
  bool get isSuccess => status == {{name.pascalCase()}}Status.success;
  bool get isFailure => status == {{name.pascalCase()}}Status.failure;
  bool get hasError => error != null && error!.isNotEmpty;
  bool get has{{entity.pascalCase()}} => {{entity.camelCase()}} != null;
  bool get has{{entity.pascalCase()}}List => {{entity.camelCase()}}List != null && {{entity.camelCase()}}List!.isNotEmpty;

  // State duration
  Duration? get stateDuration {
    if (lastUpdated == null) return null;
    return DateTime.now().difference(lastUpdated!);
  }

  @override
  String toString() {
    return '''{{name.pascalCase()}}State(
      status: $status,
      {{entity.camelCase()}}: ${{entity.camelCase()}},
      {{entity.camelCase()}}List: ${{{entity.camelCase()}}List?.length} items,
      error: $error,
      lastUpdated: $lastUpdated,
    )''';
  }
}