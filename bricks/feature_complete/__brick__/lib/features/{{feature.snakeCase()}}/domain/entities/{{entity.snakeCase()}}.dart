import 'package:equatable/equatable.dart';

/// {{entity.titleCase()}} entity
/// 
/// This entity represents a {{entity.titleCase()}} in the domain layer.
/// It contains the core business logic and rules for {{entity.titleCase()}}.
/// 
/// Generated by Mason 🧱
class {{entity.pascalCase()}} extends Equatable {
  final String id;
  final String name;
  final String description;
  final DateTime createdAt;
  final DateTime updatedAt;
  final bool isActive;

  const {{entity.pascalCase()}}({
    required this.id,
    required this.name,
    required this.description,
    required this.createdAt,
    required this.updatedAt,
    this.isActive = true,
  });

  /// Creates a copy of this {{entity.titleCase()}} with the given fields replaced
  {{entity.pascalCase()}} copyWith({
    String? id,
    String? name,
    String? description,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool? isActive,
  }) {
    return {{entity.pascalCase()}}(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isActive: isActive ?? this.isActive,
    );
  }

  /// Creates an empty {{entity.titleCase()}} instance
  factory {{entity.pascalCase()}}.empty() {
    final now = DateTime.now();
    return {{entity.pascalCase()}}(
      id: '',
      name: '',
      description: '',
      createdAt: now,
      updatedAt: now,
      isActive: true,
    );
  }

  /// Checks if this {{entity.titleCase()}} is valid
  bool get isValid {
    return id.isNotEmpty && 
           name.isNotEmpty && 
           name.trim().length >= 2;
  }

  /// Gets the display name for this {{entity.titleCase()}}
  String get displayName {
    return name.trim().isEmpty ? 'Unnamed {{entity.titleCase()}}' : name.trim();
  }

  /// Checks if this {{entity.titleCase()}} was recently created (within last 24 hours)
  bool get isRecentlyCreated {
    final now = DateTime.now();
    final difference = now.difference(createdAt);
    return difference.inHours <= 24;
  }

  /// Checks if this {{entity.titleCase()}} was recently updated (within last hour)
  bool get isRecentlyUpdated {
    final now = DateTime.now();
    final difference = now.difference(updatedAt);
    return difference.inMinutes <= 60;
  }

  @override
  List<Object?> get props => [
    id,
    name,
    description,
    createdAt,
    updatedAt,
    isActive,
  ];

  @override
  String toString() {
    return '''{{entity.pascalCase()}}(
      id: $id,
      name: $name,
      description: $description,
      createdAt: $createdAt,
      updatedAt: $updatedAt,
      isActive: $isActive,
    )''';
  }
}
