name: feature_complete
description: Creates a complete feature with all layers (domain, data, presentation)
version: 1.0.0+1

vars:
  feature:
    type: string
    description: Feature name (e.g., user_profile, product_catalog)
    prompt: What is the feature name?

  entity:
    type: string
    description: Main entity name (e.g., User, Product)
    prompt: What is the main entity name?

  has_list:
    type: boolean
    description: Does this feature need list operations?
    default: true
    prompt: Does this feature need list operations?

  has_search:
    type: boolean
    description: Does this feature need search functionality?
    default: true
    prompt: Does this feature need search functionality?

  has_pagination:
    type: boolean
    description: Does this feature need pagination?
    default: true
    prompt: Does this feature need pagination?

  api_endpoint:
    type: string
    description: API endpoint base path (e.g., /api/v1/users)
    prompt: What is the API endpoint base path?

  use_local_storage:
    type: boolean
    description: Should this feature use local storage/caching?
    default: true
    prompt: Should this feature use local storage/caching?
