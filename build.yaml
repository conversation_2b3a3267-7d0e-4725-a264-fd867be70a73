# Build configuration for code generation
targets:
  $default:
    builders:
      # Flutter Gen - Assets 和 Fonts 生成器
      flutter_gen_runner:
        enabled: true
        options:
          output: lib/generated/
          line_length: 80

      # Freezed 代码生成
      freezed:
        options:
          union_key: runtimeType
          union_value_case: snake

      # JSON 序列化
      json_serializable:
        options:
          explicit_to_json: true
          include_if_null: false

      # Retrofit API 客户端
      retrofit_generator:
        options:
          generate_to_json: true

      # Injectable 依赖注入
      injectable_generator:
        options:
          auto_register: true
