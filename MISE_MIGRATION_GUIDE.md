# Flutter Scaffold - Mise Migration Guide

## 🎉 Migration Complete!

The Flutter Scaffold project has been successfully migrated from Makefile to mise task management. All 22 original Makefile tasks are now available as mise tasks.

## ✅ What's Changed

- **Fixed PATH issues**: No more "melos: No such file or directory" errors
- **Cross-platform compatibility**: Works consistently on Windows, macOS, and Linux
- **Better task management**: Unified tool and environment management
- **Preserved functionality**: All original tasks work exactly the same
- **Fixed Windows compatibility**: Resolved PowerShell string handling issues

## 🚀 Quick Start

### Basic Commands

```bash
# Setup development environment (replaces: make setup)
mise run setup

# Bootstrap packages (replaces: make bootstrap)
mise run bootstrap

# Run tests (replaces: make test)
mise run test

# Run development server (replaces: make run-dev)
mise run run-dev
```

### Short Commands

You can use `mise r` instead of `mise run`, and each task has a short alias:

```bash
# Using mise r (short for mise run)
mise r setup
mise r test
mise r run-dev

# Using task aliases (even shorter!)
mise r s      # setup
mise r t      # test
mise r dev    # run-dev
mise r h      # help
```

## 📋 Complete Task List with Aliases

| Original Make Command | New Mise Command | Alias | Description |
|----------------------|------------------|-------|-------------|
| `make setup` | `mise run setup` | `mise r s` | Setup development environment |
| `make bootstrap` | `mise run bootstrap` | `mise r bs` | Bootstrap all packages |
| `make clean` | `mise run clean` | `mise r c` | Clean all packages |
| `make test` | `mise run test` | `mise r t` | Run tests for all packages |
| `make test-coverage` | `mise run test-coverage` | `mise r tc` | Run tests with coverage |
| `make gen` | `mise run gen` | `mise r g` | Generate code for all packages |
| `make format` | `mise run format` | `mise r f` | Format all packages |
| `make analyze` | `mise run analyze` | `mise r a` | Analyze all packages |
| `make run-dev` | `mise run run-dev` | `mise r dev` | Run development server |
| `make run-stag` | `mise run run-stag` | `mise r stag` | Run staging server |
| `make run-prod` | `mise run run-prod` | `mise r prod` | Run production server |
| `make build-dev` | `mise run build-dev` | `mise r bd` | Build APK for development |
| `make build-stag` | `mise run build-stag` | `mise r bs-stag` | Build APK for staging |
| `make build-prod` | `mise run build-prod` | `mise r bp` | Build APK for production |
| `make build-ios-dev` | `mise run build-ios-dev` | `mise r bid` | Build iOS for development |
| `make build-ios-stag` | `mise run build-ios-stag` | `mise r bis` | Build iOS for staging |
| `make build-ios-prod` | `mise run build-ios-prod` | `mise r bip` | Build iOS for production |
| `make feature` | `mise run feature` | `mise r feat` | Generate new feature |
| `make adr` | `mise run adr` | `mise r adr` | Generate new ADR |
| `make upgrade` | `mise run upgrade` | `mise r up` | Update dependencies |
| `make outdated` | `mise run outdated` | `mise r out` | Check for outdated packages |
| `make integration` | `mise run integration` | `mise r int` | Run integration tests |
| `make help` | `mise run help` | `mise r h` | Show available commands |

## ⚡ Quick Reference (Most Used Aliases)

```bash
# Development workflow
mise r s        # setup
mise r bs       # bootstrap
mise r dev      # run development server
mise r t        # test
mise r f        # format
mise r a        # analyze

# Building
mise r bd       # build development APK
mise r bp       # build production APK
mise r bid      # build iOS development
mise r bip      # build iOS production

# Code generation
mise r feat     # generate feature
mise r g        # generate code

# Utilities
mise r h        # help
mise r c        # clean
mise r up       # upgrade dependencies
```

## 🔧 Additional Mise Commands

```bash
# List all available tasks
mise tasks ls

# Get detailed info about a specific task
mise tasks info setup

# Run multiple tasks
mise run clean bootstrap test
```

## 🏗️ Development Workflow Examples

### Initial Setup
```bash
# First time setup
mise run setup
```

### Daily Development
```bash
# Start development
mise run run-dev

# Run tests
mise run test

# Format and analyze code
mise run format analyze
```

### Building for Release
```bash
# Build production APK
mise run build-prod

# Build production iOS
mise run build-ios-prod
```

### Code Generation
```bash
# Generate new feature (interactive)
mise run feature

# Generate ADR (interactive)
mise run adr

# You can also pass parameters directly:
# mise run feature --name my_feature
# mise run adr --name "My Decision"
```

## 🔄 Backward Compatibility

The original Makefile is still present and functional. You can continue using `make` commands if needed, but we recommend migrating to `mise` for better cross-platform support.

## 🎯 Benefits of Migration

1. **Solved PATH Issues**: No more environment variable problems
2. **Cross-Platform**: Works identically on Windows, macOS, Linux
3. **Tool Management**: Automatic Flutter/Java version management
4. **Better UX**: Clearer task descriptions and help
5. **Future-Proof**: Modern task runner with active development

## 📚 Learn More

- [mise Documentation](https://mise.jdx.dev/)
- [mise Task Configuration](https://mise.jdx.dev/tasks/)
- [Flutter Scaffold Documentation](./README.md)

---

**Note**: This migration maintains 100% functional compatibility with the original Makefile while solving the Windows PATH issues and providing better cross-platform support.
