# 🚀 Flutter 企业级脚手架

<div align="center">

![Flutter](https://img.shields.io/badge/Flutter-3.32.5-blue.svg)
![Dart](https://img.shields.io/badge/Dart-3.6.0-blue.svg)
![Clean Architecture](https://img.shields.io/badge/Architecture-Clean-green.svg)
![License](https://img.shields.io/badge/License-MIT-green.svg)

**一个遵循 Clean Architecture 的现代 Flutter 企业级脚手架**

[快速开始](#-快速开始) • [架构概览](#-架构概览) • [功能特性](#-功能特性) • [开发指南](#-开发指南) • [部署指南](#-部署指南)

</div>

---

## 📋 项目简介

这是一个 **企业级 Flutter 脚手架项目**，严格遵循 **Clean Architecture** 原则，为生产环境应用提供完整的工程化解决方案。

### 🎯 核心目标

- **🏗️ 架构驱动**：Clean Architecture + DDD 思想
- **🔧 工程化**：完整的开发工具链和自动化流程
- **🚀 高性能**：优化的构建和运行时性能
- **🧪 质量保证**：全面的测试策略和代码质量监控
- **📚 文档完善**：详细的架构文档和开发指南

---

## ✨ 核心特性

### 🏗️ 架构特性
- **Clean Architecture**：严格的三层架构分离
- **领域驱动设计**：DDD 思想的实践应用
- **依赖注入**：GetIt + Injectable 编译时生成
- **状态管理**：BLoC Pattern + Repository Pattern
- **类型安全**：全程强类型约束，零运行时错误

### 🛠️ 技术栈
- **状态管理**：BLoC + flutter_bloc
- **路由导航**：GoRouter + 路由守卫
- **数据库**：Drift (SQLite ORM)
- **网络请求**：Dio + 拦截器系统
- **依赖注入**：GetIt + Injectable
- **代码生成**：build_runner + freezed + json_serializable
- **主题系统**：FlexColorScheme
- **测试框架**：mocktail + bloc_test + patrol

### 🚀 开发工具
- **Monorepo 管理**：Melos
- **代码生成**：Mason (14种模板)
- **构建工具**：Fastlane
- **质量检查**：自定义 Makefile
- **CI/CD**：GitHub Actions

---

## 🚀 快速开始

### 📋 环境要求

```bash
# 必需工具
Flutter >= 3.32.5
Dart >= 3.6.0
Git

# 可选工具（推荐）
Make       # 快捷命令
Melos      # Monorepo 管理
Mason      # 代码生成
```

### 🛠️ 一键安装

```bash
# 1. 克隆项目
git clone https://github.com/your-org/flutter-scaffold.git
cd flutter-scaffold

# 2. 自动化安装
make setup

# 3. 启动开发环境
make run-dev
```

### 📦 手动安装

```bash
# 1. 安装全局工具
dart pub global activate melos
dart pub global activate mason_cli
dart pub global activate patrol_cli

# 2. 安装项目依赖
melos bootstrap

# 3. 生成代码
make gen

# 4. 运行分析
make analyze

# 5. 运行测试
make test
```

---

## 🏗️ 架构概览

### 📊 三层架构图

```
┌─────────────────────────────────────────────────────────────┐
│                    表现层 (Presentation)                      │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │    Pages       │  │    Widgets      │  │     BLoC        │ │
│  │   (页面)        │  │   (组件)        │  │  (状态管理)      │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│                    领域层 (Domain)                            │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │   Entities      │  │   Use Cases     │  │  Repositories   │ │
│  │   (实体)        │  │   (用例)        │  │  (仓储接口)      │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│                    数据层 (Data)                              │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │ Repositories    │  │  Data Sources   │  │    Models       │ │
│  │ (仓储实现)       │  │  (数据源)       │  │   (数据模型)     │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 📁 项目结构

```bash
flutter_scaffold/
├── apps/mobile/                    # 主应用
│   ├── lib/
│   │   ├── core/                  # 核心基础设施
│   │   │   ├── di/               # 依赖注入
│   │   │   ├── error/            # 错误处理
│   │   │   ├── router/           # 路由系统
│   │   │   ├── theme/            # 主题系统
│   │   │   ├── network/          # 网络层
│   │   │   ├── database/         # 数据库
│   │   │   └── usecase/          # 用例基类
│   │   ├── features/              # 功能模块
│   │   │   └── auth/             # 认证模块示例
│   │   │       ├── data/         # 数据层
│   │   │       ├── domain/       # 领域层
│   │   │       └── presentation/ # 表现层
│   │   └── main_*.dart           # 环境入口
│   ├── test/                     # 测试文件
│   ├── fastlane/                 # 自动化部署
│   └── coverage/                 # 覆盖率报告
├── packages/                     # 共享包
│   ├── ui_library/               # UI 组件库
│   └── data_models/              # 共享数据模型
├── bricks/                      # Mason 代码模板
├── docs/                        # 项目文档
├── .github/                     # CI/CD 配置
├── melos.yaml                   # Monorepo 配置
└── Makefile                     # 快捷命令
```

---

## 🧱 Mason 代码生成系统

### 📋 模板总览

| 模板 | 命令 | 生成内容 | 适用场景 |
|------|------|----------|----------|
| **feature** | `mason make feature` | 完整功能模块 | 新功能开发 |
| **model** | `mason make model` | 数据模型 | 数据结构定义 |
| **repository** | `mason make repository` | 仓储层 | 数据访问层 |
| **usecase** | `mason make usecase` | 用例类 | 业务逻辑封装 |
| **bloc** | `mason make bloc` | 状态管理 | 页面状态管理 |
| **page** | `mason make page` | 页面组件 | UI 页面开发 |
| **widget** | `mason make widget` | 可复用组件 | UI 组件开发 |
| **adr** | `mason make adr` | 架构决策记录 | 技术决策文档 |
| **api_client** | `mason make api_client` | API 客户端 | 外部 API 集成 |
| **validator** | `mason make validator` | 数据验证器 | 输入验证 |
| **service** | `mason make service` | 服务类 | 业务服务 |

### 🚀 快速生成示例

#### 1. 创建完整功能模块
```bash
# 创建用户管理功能
mason make feature --name user_management \
  --entity true \
  --repository true \
  --usecase true \
  --bloc true \
  --page true \
  --tests true
```

#### 2. 创建特定组件
```bash
# 创建页面
mason make page --name user_profile --feature user_management

# 创建数据模型
mason make model --name user --feature user_management

# 创建用例
mason make usecase --name get_user_list --feature user_management
```

#### 3. 架构决策记录
```bash
# 记录技术决策
mason make adr --name "implement-user-authentication"
```

---

## 🛠️ 开发指南

### 🔧 日常开发命令

```bash
# 环境管理
make setup          # 初始化开发环境
make bootstrap      # 安装所有依赖
make clean          # 清理构建文件

# 代码质量
make analyze        # 静态代码分析
make format         # 代码格式化
make test           # 运行所有测试
make test-coverage  # 生成测试覆盖率报告

# 代码生成
make gen            # 生成所有代码
flutter pub run build_runner watch  # 监听模式

# 开发运行
make run-dev        # 开发环境
make run-stag       # 测试环境
make run-prod       # 生产环境

# 构建部署
make build-dev      # 构建 Development APK
make build-stag     # 构建 Staging APK
make build-prod     # 构建 Production APK
```

### 🧪 测试策略

#### 测试金字塔
```
┌─────────────────┐
│   集成测试       │ 10% - 关键业务流程
├─────────────────┤
│   组件测试       │ 30% - BLoC/页面测试
├─────────────────┤
│   单元测试       │ 60% - 用例/仓储测试
└─────────────────┘
```

#### 测试命令
```bash
# 运行所有测试
make test

# 运行特定类型测试
flutter test test/unit/
flutter test test/widget/

# 生成覆盖率报告
make test-coverage

# 运行集成测试
make integration
```

### 🌍 多环境配置

#### 环境文件
```dart
// main_development.dart - 开发环境
void main() async {
  await di.configureDependencies();
  await AuthGuard.initialize();
  runApp(const App());
}

// main_staging.dart - 测试环境
void main() async {
  await di.configureDependencies();
  await AuthGuard.initialize();
  runApp(const App());
}

// main_production.dart - 生产环境
void main() async {
  await di.configureDependencies();
  await AuthGuard.initialize();
  runApp(const App());
}
```

#### 环境特定配置
- **开发环境**：调试日志、热重载、模拟数据
- **测试环境**：真实数据、性能监控、错误报告
- **生产环境**：优化构建、安全配置、性能监控

---

## 🔐 核心功能模块

### 📱 认证系统
- **多种登录方式**：短信、微信、邮箱密码
- **安全机制**：Token 管理、会话保持
- **路由守卫**：自动身份验证和页面保护
- **状态管理**：完整的认证状态管理

### 🌐 网络层
- **Dio 客户端**：HTTP 请求封装
- **拦截器系统**：
  - Token 认证拦截器
  - 请求缓存拦截器
  - 重试机制拦截器
  - 请求去重拦截器
- **错误处理**：统一错误处理和重试机制

### 🗄️ 数据库
- **Drift ORM**：类型安全的数据库操作
- **数据迁移**：自动数据库版本管理
- **缓存策略**：本地数据缓存和同步
- **性能优化**：查询优化和索引管理

### 🎨 主题系统
- **FlexColorScheme**：现代化主题系统
- **动态主题**：深色/浅色模式切换
- **品牌定制**：自定义颜色和字体
- **响应式设计**：适配不同屏幕尺寸

---

## 🚀 部署指南

### 📱 移动端部署

#### Android 部署
```bash
# 构建 APK
make build-dev
make build-stag
make build-prod

# 构建 AAB (Google Play 推荐)
flutter build appbundle --flavor production

# 使用 Fastlane 部署
cd apps/mobile/android
fastlane deploy_production
```

#### iOS 部署
```bash
# 构建 iOS
make build-ios-dev
make build-ios-stag
make build-ios-prod

# 使用 Fastlane 部署
cd apps/mobile/ios
fastlane deploy_production
```

### 🔄 CI/CD 流水线

#### GitHub Actions
```yaml
# .github/workflows/ci.yml
name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: subosito/flutter-action@v2
      - run: make setup
      - run: make test
      - run: make test-coverage

  build:
    needs: test
    runs-on: ubuntu-latest
    steps:
      - run: make build-prod
      - run: make build-ios-prod
```

#### 部署策略
- **开发分支** → Firebase App Distribution
- **测试分支** → TestFlight / 内部测试
- **主分支** → 应用商店发布

---

## 📊 性能优化

### ⚡ 构建优化
```bash
# 开发构建（快速）
flutter build apk --flavor development --dart-define=ENV=dev

# 生产构建（优化）
flutter build apk --flavor production --dart-define=ENV=prod \
  --shrink --obfuscate --split-debug-info=debug/
```

### 🎯 运行时优化
- **懒加载**：延迟加载非关键资源
- **缓存策略**：内存缓存 + 磁盘缓存
- **内存管理**：图片优化、内存泄漏检测
- **启动优化**：启动时间分析和优化

### 📈 性能监控
- **Flutter DevTools**：性能分析工具
- **Firebase Performance**：应用性能监控
- **Sentry**：错误跟踪和性能监控
- **自定义指标**：业务性能指标监控

---

## 🧪 质量保证

### 📋 代码质量标准
- **代码覆盖率**：≥ 80%
- **代码风格**：统一格式化规则
- **类型安全**：全程强类型约束
- **错误处理**：统一的错误处理机制

### 🔍 静态分析
```bash
# 代码分析
make analyze

# 依赖分析
flutter pub deps

# 代码检查
flutter analyze --fatal-infos
```

### 📊 测试覆盖率
```bash
# 生成覆盖率报告
make test-coverage

# 查看覆盖率报告
open coverage/index.html
```

---

## 🔧 故障排除

### 🐛 常见问题

#### 1. 依赖冲突
```bash
# 清理并重新安装
make clean && make bootstrap

# 检查依赖冲突
flutter pub deps --style=tree
```

#### 2. 代码生成失败
```bash
# 🚨 紧急修复（推荐）
./scripts/fix_codegen.sh

# 手动修复
flutter pub run build_runner clean
make gen

# 分步生成（如果遇到循环依赖）
dart run build_runner build --build-filter="**/*.freezed.dart" --delete-conflicting-outputs
dart run build_runner build --build-filter="**/*.g.dart" --delete-conflicting-outputs
dart run build_runner build --build-filter="**/*.config.dart" --delete-conflicting-outputs
```

**详细故障排除指南：**
- 📖 [代码生成故障排除详细指南](docs/CODE_GENERATION_TROUBLESHOOTING.md)
- 🚀 [代码生成快速参考](docs/CODE_GENERATION_QUICK_REFERENCE.md)

#### 3. 测试失败
```bash
# 清理并重新运行测试
flutter clean && flutter test

# 运行特定测试
flutter test test/unit/features/auth/
```

### 🛠️ 调试工具
```bash
# 查看依赖树
flutter pub deps

# 分析 APK 大小
flutter build apk --analyze-size

# 性能分析
flutter run --profile

# 内存分析
flutter run --profile --debug
```

---

## 📚 项目文档

### 📖 核心文档
- [架构设计指南](docs/CLEAN_ARCHITECTURE_GUIDE_PRO.md)
- [开发指南](docs/DEVELOPMENT_GUIDE.md)
- [状态管理指南](docs/STATE_MANAGEMENT_GUIDE.md)
- [UI 设计指南](docs/UI_GUIDE_PRO.md)
- [后端 API 集成指南](docs/BACKEND_API_GUIDE.md)

### 📋 架构决策记录
- [ADR 记录](docs/adr/) - 架构决策历史记录
- [ADR 模板](docs/adr/adr-template.md) - 决策记录模板

### 🎯 学习资源
- [Flutter 官方文档](https://flutter.dev/docs)
- [Clean Architecture 原理](https://blog.cleancoder.com/uncle-bob/2012/08/13/the-clean-architecture.html)
- [BLoC 模式教程](https://bloclibrary.dev/)
- [Mason 文档](https://docs.brickhub.dev/)

---

## 🤝 贡献指南

### 📝 提交规范
```bash
# 提交信息格式
<type>(<scope>): <subject>

# 类型说明
✨ feat      新功能
🐛 fix       Bug 修复
📚 docs      文档更新
🎨 style     代码格式
♻️ refactor  代码重构
⚡ perf      性能优化
✅ test      测试相关
🔧 chore     构建工具
```

### 🔄 开发流程
1. **Fork** 项目到个人仓库
2. **Feature** 分支开发
3. **测试** 确保功能正常
4. **提交** 遵循提交规范
5. **PR** 提交合并请求
6. **Review** 代码审查
7. **Merge** 合并到主分支

---

## 📄 许可证

本项目采用 [MIT 许可证](LICENSE) - 查看 [LICENSE](LICENSE) 文件了解详情。

---

## 🙏 致谢

感谢以下开源项目：

- [Flutter](https://flutter.dev/) - 跨平台 UI 框架
- [BLoC](https://bloclibrary.dev/) - 状态管理库
- [GetIt](https://pub.dev/packages/get_it) - 依赖注入
- [Melos](https://melos.invertase.dev/) - Monorepo 管理
- [Mason](https://github.com/felangel/mason) - 代码生成工具

---

## 📞 联系我们

- **问题反馈**：[GitHub Issues](https://github.com/your-org/flutter-scaffold/issues)
- **功能建议**：[GitHub Discussions](https://github.com/your-org/flutter-scaffold/discussions)
- **邮件联系**：[<EMAIL>](mailto:<EMAIL>)

---

<div align="center">

**⭐ 如果这个项目对您有帮助，请给我们一个 Star！**

![Star History](https://img.shields.io/github/stars/your-org/flutter-scaffold?style=social)

</div>