// --- Flutter/Dart Specific Terms ---
// --- Project Specific Terms ---
// Custom types and domain specific nouns
// Flutter framework and Dart language specific terms
accessToken
AppUser
ARGB
AuthBloc
AuthEvent
AuthState
AuthUser
bezier
BlocBuilder
BlocConsumer
BlocListener
BlocProvider
blocTest
BuildContext
buntdb
cartID
casbin
clearInteractions
contextx
critep
DioException
dkron
Dkron
dotenv
emqx
errorsc
fsnotify
gatspy
ginx
glog
gomock
gormlog
grey
htmls
Infof
ionide
JSSDK
lerp
Mchid
meili
meilisearch
melos
mmopen
mockito
mocktail
mqtt
MQTT
MQTTAPI
mysql
nats
Nats
nginx
Noto
ordernum
OrderStatus
pkill
prefs
Prefs
pumpAndSettle
pumpWidget
qiniu
qlogo
redis
refreshToken
remoteDataSource
rgba
saas
scaffold
SKU
sqlmock
StatefulWidget
StatelessWidget
stretchr
templ
testWidgets
thirdwx
timex
toEntity
tokenExpiry
unfocus
unionid
unobserve
variantID
verifyNever
vsync
Warnf
wechat
Wechat
WeChat
whenListen
WidgetTester
xingcheng
