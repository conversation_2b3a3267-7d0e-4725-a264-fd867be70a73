[tools]
flutter = "3.32.5"
java = "23.0.2"

[hooks]

[env]
APP_DIR = "apps/mobile"
FLUTTER_VERSION = "3.32.5"

# Original Flutter tasks (kept for compatibility)
[tasks.flutter]
description = "run get flutter version"
alias = "version"
run = "flutter --version"

[tasks.flutter-doctor]
description = "run flutter doctor"
alias = "doctor"
run = "flutter doctor -v"

[tasks.pub-get]
description = "run get flutter project pub"
run = "flutter pub get"

[tasks.dev-android]
alias = "dev-and"
description = "run for android"
run = "flutter run --no-sound-null-safety -d M2012K11AC"
depends = ['pub-get']

[tasks.prod-android]
alias = "prod-and"
description = "build apk for android"
run = "flutter build apk --release --flavor production -t lib/main_production.dart"
depends = ['pub-get']

# Flutter Scaffold tasks (migrated from Makefile)
# Development environment setup
[tasks.setup]
description = "Setup development environment"
alias = "s"
run = [
  "echo 'Setting up Flutter Scaffold development environment...'",
  "echo 'Installing Melos...'",
  "dart pub global activate melos",
  "echo 'Installing Mason...'",
  "dart pub global activate mason_cli",
  "echo 'Installing Patrol...'",
  "dart pub global activate patrol_cli",
  "echo 'Bootstrap packages...'",
  "dart pub global run melos bootstrap",
  "echo 'Setup complete! 🚀'"
]

[tasks.bootstrap]
description = "Bootstrap all packages"
alias = "bs"
run = "dart pub global run melos bootstrap"

[tasks.clean]
description = "Clean all packages"
alias = "c"
run = "dart pub global run melos run clean"

[tasks.test]
description = "Run tests for all packages"
alias = "t"
run = "dart pub global run melos run test"

[tasks.test-coverage]
description = "Run tests with coverage"
alias = "tc"
run = "dart pub global run melos run test --coverage"

[tasks.gen]
description = "Generate code for all packages"
alias = "g"
run = "dart pub global run melos run gen"

[tasks.format]
description = "Format all packages"
alias = "f"
run = "dart pub global run melos run format"

[tasks.analyze]
description = "Analyze all packages"
alias = "a"
run = "dart pub global run melos run analyze"

# Run tasks
[tasks.run-dev]
description = "Run development server"
alias = "dev"
run = "flutter run --flavor development -t lib/main_development.dart"
dir = "apps/mobile"

[tasks.run-stag]
description = "Run staging server"
alias = "stag"
run = "flutter run --flavor staging -t lib/main_staging.dart"
dir = "apps/mobile"

[tasks.run-prod]
description = "Run production server"
alias = "prod"
run = "flutter run --flavor production -t lib/main_production.dart"
dir = "apps/mobile"

# Build tasks
[tasks.build-dev]
description = "Build APK for development"
alias = "bd"
run = "flutter build apk --flavor development -t lib/main_development.dart"
dir = "apps/mobile"

[tasks.build-stag]
description = "Build APK for staging"
alias = "bs-stag"
run = "flutter build apk --flavor staging -t lib/main_staging.dart"
dir = "apps/mobile"

[tasks.build-prod]
description = "Build APK for production"
alias = "bp"
run = "flutter build apk --flavor production -t lib/main_production.dart"
dir = "apps/mobile"

# iOS build tasks
[tasks.build-ios-dev]
description = "Build iOS for development"
alias = "bid"
run = "flutter build ios --flavor development -t lib/main_development.dart --no-codesign"
dir = "apps/mobile"

[tasks.build-ios-stag]
description = "Build iOS for staging"
alias = "bis"
run = "flutter build ios --flavor staging -t lib/main_staging.dart --no-codesign"
dir = "apps/mobile"

[tasks.build-ios-prod]
description = "Build iOS for production"
alias = "bip"
run = "flutter build ios --flavor production -t lib/main_production.dart --no-codesign"
dir = "apps/mobile"

# Mason Templates - Code Generation
[tasks.feature-complete]
description = "Generate complete feature with all layers (interactive)"
alias = "fc"
run = "mason make feature_complete"

[tasks.bloc]
description = "Generate BLoC pattern (interactive)"
alias = "bloc"
run = "mason make bloc"

[tasks.repository]
description = "Generate Repository pattern (interactive)"
alias = "repo"
run = "mason make repository"

[tasks.usecase]
description = "Generate UseCase pattern (interactive)"
alias = "uc"
run = "mason make usecase"

[tasks.feature]
description = "Generate legacy feature (interactive)"
alias = "feat"
run = "mason make feature"

[tasks.adr]
description = "Generate new ADR (interactive)"
alias = "adr"
run = "mason make adr"

[tasks.mason-list]
description = "List all available Mason templates"
alias = "ml"
run = "mason list"

[tasks.mason-info]
description = "Get Mason template information (interactive)"
alias = "mi"
run = [
  "echo 'Available templates:'",
  "mason list",
  "echo ''",
  "read -p 'Enter template name for info: ' template",
  "mason info $template"
]

# Dependency management
[tasks.upgrade]
description = "Update dependencies"
alias = "up"
run = "dart pub global run melos run upgrade"

[tasks.outdated]
description = "Check for outdated packages"
alias = "out"
run = "dart pub global run melos exec -- flutter pub outdated"

# Testing
[tasks.integration]
description = "Run integration tests"
alias = "int"
run = "patrol test integration_test"
dir = "apps/mobile"

# Help
[tasks.help]
description = "Show available commands"
alias = "h"
run = "mise tasks ls"


