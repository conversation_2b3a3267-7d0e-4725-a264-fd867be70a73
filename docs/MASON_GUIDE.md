# Flutter Scaffold - Mason 代码模板使用指南

## 🧱 什么是 Mason？

Mason 是一个强大的 Dart 代码生成工具，可以通过预定义的模板快速生成代码。本项目提供了 **13 种专业模板**，支持 Clean Architecture 的完整开发流程。

## � Mason + build_runner 工作原理

### 工作流程图

```mermaid
graph TD
    A[Mason 模板] --> B[生成 Dart 文件]
    B --> C[包含注解的代码]
    C --> D[build_runner 处理]
    D --> E[生成辅助文件]

    F[@freezed] --> G[.freezed.dart]
    H[@JsonSerializable] --> I[.g.dart]
    J[@injectable] --> K[.config.dart]

    C --> F
    C --> H
    C --> J
```

### 两阶段代码生成

1. **第一阶段 - Mason 模板生成**
   - 生成包含注解的 Dart 源文件
   - 创建项目结构和基础代码

2. **第二阶段 - build_runner 代码生成**
   - 处理 `@freezed`、`@JsonSerializable`、`@injectable` 等注解
   - 生成 `.freezed.dart`、`.g.dart`、`.config.dart` 等辅助文件

## �📋 模板总览

| 模板名称 | 用途 | 生成内容 | 需要 build_runner | 命令示例 |
|---------|------|----------|------------------|----------|
| **feature** | 完整功能模块 | 三层架构完整代码 | ✅ | `mason make feature` |
| **model** | 数据模型 | Entity + Model + 测试 | ✅ | `mason make model` |
| **repository** | 仓储层 | 接口 + 实现类 | ❌ | `mason make repository` |
| **usecase** | 用例类 | 业务逻辑封装 | ❌ | `mason make usecase` |
| **bloc** | 状态管理 | BLoC + Event + State | ✅ | `mason make bloc` |
| **page** | 页面组件 | UI 页面 + 测试 | ❌ | `mason make page` |
| **widget** | 可复用组件 | UI 组件 | ❌ | `mason make widget` |
| **adr** | 架构决策记录 | 技术决策文档 | ❌ | `mason make adr` |
| **api_client** | API 客户端 | 外部 API 集成 | ✅ | `mason make api_client` |
| **validator** | 数据验证器 | 输入验证逻辑 | ❌ | `mason make validator` |
| **service** | 服务类 | 业务服务 | ❌ | `mason make service` |
| **data_source** | 数据源 | 本地/远程数据源 | ❌ | `mason make data_source` |
| **test** | 测试文件 | 单元/集成测试 | ❌ | `mason make test` |

## � 完整开发工作流程

### 标准开发流程

```bash
# 1. 使用 Mason 生成代码模板
mason make feature --name user_management

# 2. 运行 build_runner 生成辅助代码
make gen
# 或者
melos run gen
# 或者
dart run build_runner build --delete-conflicting-outputs

# 3. 实现业务逻辑
# 编辑生成的文件，添加具体实现

# 4. 验证代码
make analyze
make test
```

### 监听模式开发

```bash
# 启动 build_runner 监听模式
dart run build_runner watch

# 在另一个终端使用 Mason 生成代码
mason make model --name product
# build_runner 会自动检测并生成相应的 .g.dart 和 .freezed.dart 文件
```

## �🚀 核心模板详解

### 1. Feature 模板（最常用）

创建完整的 Clean Architecture 功能模块，**包含需要 build_runner 处理的注解**。

#### 基本用法
```bash
# 创建完整功能模块
mason make feature --name user_management

# 交互式创建（推荐）
mason make feature
```

#### 高级用法
```bash
# 创建包含所有组件的功能
mason make feature --name user_management \
  --entity true \
  --repository true \
  --usecase true \
  --bloc true \
  --page true \
  --tests true
```

#### ⚠️ 重要：生成后必须运行 build_runner
```bash
# Feature 模板包含 @freezed 注解，需要代码生成
make gen
```

#### 生成的文件结构
```
lib/features/user_management/
├── data/
│   ├── datasources/
│   │   ├── user_management_local_data_source.dart
│   │   └── user_management_remote_data_source.dart
│   ├── models/
│   │   └── user_management_model.dart
│   └── repositories/
│       └── user_management_repository_impl.dart
├── domain/
│   ├── entities/
│   │   └── user_management.dart
│   ├── repositories/
│   │   └── user_management_repository.dart
│   └── usecases/
│       ├── create_user_management.dart
│       ├── delete_user_management.dart
│       ├── get_all_user_managements.dart
│       ├── get_user_management.dart
│       └── update_user_management.dart
├── presentation/
│   ├── bloc/
│   │   ├── user_management_bloc.dart
│   │   ├── user_management_event.dart
│   │   └── user_management_state.dart
│   ├── pages/
│   │   └── user_management_page.dart
│   └── widgets/
│       └── user_management_widget.dart
└── test/
    ├── unit/
    │   └── domain/
    │       └── usecases/
    │           └── create_user_management_test.dart
    └── widget/
        └── presentation/
            └── pages/
                └── user_management_page_test.dart
```

### 2. Model 模板

创建数据模型和实体，**包含 @freezed 和 @JsonSerializable 注解**。

#### 基本用法
```bash
# 创建数据模型
mason make model --name user

# 指定功能模块
mason make model --name user --feature auth
```

#### 生成的代码示例
```dart
// 生成的 user_model.dart 包含注解
import 'package:freezed_annotation/freezed_annotation.dart';
import '../../domain/entities/user.dart';

part 'user_model.freezed.dart';  // 需要 build_runner 生成
part 'user_model.g.dart';        // 需要 build_runner 生成

@freezed
abstract class UserModel with _$UserModel {
  const UserModel._();

  const factory UserModel({
    required String id,
    required String name,
  }) = _UserModel;

  factory UserModel.fromJson(Map<String, dynamic> json) =>
      _$UserModelFromJson(json);  // build_runner 生成的方法

  User toEntity() {
    return User(id: id, name: name);
  }
}
```

#### ⚠️ 重要：生成后必须运行 build_runner
```bash
# Model 模板包含 @freezed 和 @JsonSerializable 注解
make gen
```

#### 生成的文件结构
```
lib/features/auth/data/models/
├── user_model.dart           # Mason 生成
├── user_model.freezed.dart   # build_runner 生成
└── user_model.g.dart         # build_runner 生成
lib/features/auth/domain/entities/user.dart  # Mason 生成
```

### 3. API Client 模板

创建 Retrofit API 客户端，**包含 @RestApi 注解**。

#### 基本用法
```bash
# 创建 API 客户端
mason make api_client --name user
```

#### 生成的代码示例
```dart
// 生成的 user_api_client.dart 包含注解
import 'package:dio/dio.dart';
import 'package:retrofit/retrofit.dart';
import '../models/user_model.dart';

part 'user_api_client.g.dart';  // 需要 build_runner 生成

@RestApi(baseUrl: '/api/v1/users')
abstract class UserApiClient {
  factory UserApiClient(Dio dio, {String baseUrl}) = _UserApiClient;

  @GET('/')
  Future<List<UserModel>> getUsers();

  @GET('/{id}')
  Future<UserModel> getUser(@Path('id') String id);

  @POST('/')
  Future<UserModel> createUser(@Body() UserModel user);
}
```

#### ⚠️ 重要：生成后必须运行 build_runner
```bash
# API Client 模板包含 @RestApi 注解
make gen
```

### 4. Repository 模板

创建仓储模式实现，**不需要 build_runner**。

#### 基本用法
```bash
# 创建仓储
mason make repository --name user_repository

# 指定功能模块
mason make repository --name user_repository --feature auth
```

#### 生成的文件结构
```
lib/features/auth/data/repositories/user_repository_impl.dart
lib/features/auth/domain/repositories/user_repository.dart
```

### 5. BLoC 模板

创建状态管理代码。

#### 基本用法
```bash
# 创建 BLoC
mason make bloc --name auth

# 指定功能模块
mason make bloc --name auth --feature auth
```

#### 生成的文件结构
```
lib/features/auth/presentation/bloc/auth_bloc.dart
lib/features/auth/presentation/bloc/auth_event.dart
lib/features/auth/presentation/bloc/auth_state.dart
```

### 5. Page 模板

创建页面组件。

#### 基本用法
```bash
# 创建页面
mason make page --name login

# 指定功能模块
mason make page --name login --feature auth
```

#### 生成的文件结构
```
lib/features/auth/presentation/pages/login_page.dart
```

## 🔧 实际开发场景

### 场景1：用户管理系统

```bash
# 1. 创建主功能模块
mason make feature --name user_management \
  --entity true \
  --repository true \
  --usecase true \
  --bloc true \
  --page true \
  --tests true

# 2. 创建相关页面
mason make page --name user_list --feature user_management
mason make page --name user_detail --feature user_management
mason make page --name user_edit --feature user_management

# 3. 创建数据模型
mason make model --name user_profile --feature user_management
mason make model --name user_role --feature user_management

# 4. 创建专用用例
mason make usecase --name search_users --feature user_management
mason make usecase --name export_users --feature user_management

# 5. 创建验证器
mason make validator --name user_input --feature user_management

# 6. 记录架构决策
mason make adr --name "user-management-system-design"
```

### 场景2：电商应用

```bash
# 1. 创建核心功能
mason make feature --name product_catalog \
  --entity true \
  --repository true \
  --usecase true \
  --bloc true \
  --page true

mason make feature --name shopping_cart \
  --entity true \
  --repository true \
  --usecase true \
  --bloc true \
  --page true

mason make feature --name order_management \
  --entity true \
  --repository true \
  --usecase true \
  --bloc true \
  --page true

# 2. 创建 API 客户端
mason make api_client --name payment_gateway

# 3. 创建服务
mason make service --name inventory_service
mason make service --name notification_service

# 4. 创建数据源
mason make data_source --name local_cache
mason make data_source --name remote_api
```

### 场景3：快速原型开发

```bash
# 1. 快速创建 MVP 功能
mason make feature --name prototype_feature \
  --entity true \
  --bloc false \
  --page true \
  --tests false

# 2. 后续完善
mason make bloc --name prototype_feature --feature prototype_feature
mason make repository --name prototype_feature --feature prototype_feature
mason make usecase --name get_prototype_data --feature prototype_feature
```

## 🎨 模板变量系统

所有模板支持智能变量替换：

| 变量 | 示例 | 说明 |
|------|------|------|
| `{{name}}` | `user_management` | 原始名称 |
| `{{name.pascalCase}}` | `UserManagement` | 帕斯卡命名 |
| `{{name.snakeCase}}` | `user_management` | 下划线命名 |
| `{{name.camelCase}}` | `userManagement` | 驼峰命名 |
| `{{feature}}` | `auth` | 功能模块名 |
| `{{feature.pascalCase}}` | `Auth` | 功能模块帕斯卡命名 |

## 📝 最佳实践

### 1. 命名规范
```bash
# ✅ 良好的命名
mason make feature --name user_profile
mason make feature --name order_management
mason make feature --name product_catalog

# ❌ 避免的命名
mason make feature --name userprofile
mason make feature --name UserManagement
mason make feature --name product-catalog
```

### 2. 功能模块设计
```bash
# ✅ 合理的功能划分
mason make feature --name user_authentication
mason make feature --name user_profile
mason make feature --name user_settings

# ❌ 过大的功能模块
mason make feature --name user_everything
```

### 3. 渐进式开发
```bash
# 第一步：创建基础结构
mason make feature --name new_feature --entity true --page true

# 第二步：添加业务逻辑
mason make usecase --name process_new_feature --feature new_feature
mason make bloc --name new_feature --feature new_feature

# 第三步：完善测试
mason make test --name new_feature_unit --feature new_feature --type unit
mason make test --name new_feature_widget --feature new_feature --type widget
```

## 🔧 高级配置

### 自定义模板
```bash
# 创建自定义模板
mason make feature --name custom_template --custom-template

# 使用自定义配置
mason make feature --name my_feature --config custom_config.json
```

### 批量生成
```bash
# 创建多个功能模块
for feature in auth user product order; do
  mason make feature --name $feature
done
```

### 集成到 CI/CD
```yaml
# .github/workflows/generate.yml
- name: Generate code
  run: |
    mason make feature --name ci_generated_feature
    make gen
```

## � build_runner 配置详解

### build.yaml 配置文件

项目根目录的 `build.yaml` 文件控制所有代码生成行为：

```yaml
targets:
  $default:
    builders:
      # Freezed 配置
      freezed:
        options:
          union_key: runtimeType        # Union 类型的键名
          union_value_case: snake       # 值的命名格式

      # JSON 序列化配置
      json_serializable:
        options:
          explicit_to_json: true        # 显式调用 toJson()
          include_if_null: false        # 排除 null 值字段

      # Injectable 依赖注入配置
      injectable_generator:
        options:
          auto_register: true           # 自动注册依赖

      # Retrofit API 客户端配置
      retrofit_generator:
        options:
          generate_to_json: true        # 生成 toJson 方法
```

### 常用 build_runner 命令

```bash
# 一次性生成所有代码
dart run build_runner build --delete-conflicting-outputs

# 监听模式（推荐开发时使用）
dart run build_runner watch --delete-conflicting-outputs

# 清理生成的文件
dart run build_runner clean

# 使用项目 Makefile（推荐）
make gen        # 等同于 build
make gen-watch  # 等同于 watch
make gen-clean  # 等同于 clean
```

### Mason + build_runner 最佳实践

#### 1. 开发时工作流
```bash
# 启动监听模式
dart run build_runner watch &

# 使用 Mason 生成代码
mason make model --name product
# build_runner 自动检测并生成 .freezed.dart 和 .g.dart

# 实现业务逻辑
# 编辑生成的文件...

# 验证代码
make analyze
```

#### 2. CI/CD 工作流
```bash
# 在 CI 中一次性生成所有代码
dart run build_runner build --delete-conflicting-outputs
dart analyze
flutter test
```

## �🚀 故障排除

### build_runner 相关问题

#### 1. 生成文件冲突
```bash
# 错误：Conflicting outputs were detected
# 解决：使用 --delete-conflicting-outputs 标志
dart run build_runner build --delete-conflicting-outputs
```

#### 2. 部分文件未生成
```bash
# 清理并重新生成
dart run build_runner clean
dart run build_runner build --delete-conflicting-outputs
```

#### 3. 监听模式不工作
```bash
# 停止监听进程
pkill -f "build_runner watch"

# 重新启动
dart run build_runner watch --delete-conflicting-outputs
```

### Mason 相关问题

#### 1. 模板不存在
```bash
# 检查模板列表
mason list

# 重新安装模板
mason add git://github.com/your-org/flutter-scaffold-bricks
```

#### 2. 变量替换失败
```bash
# 检查变量名是否正确
mason make feature --name correct_name

# 查看模板帮助
mason make feature --help
```

#### 3. 文件冲突
```bash
# 删除冲突文件后重新生成
rm lib/features/old_feature/
mason make feature --name new_feature
```

### 调试技巧
```bash
# 启用详细输出
mason make feature --name debug_feature --verbose

# 检查模板路径
mason doctor

# 验证生成的代码
make analyze
make test
```

## 📚 总结

Mason + build_runner 双重代码生成系统是 Flutter Scaffold 项目的核心功能，它们协同工作：

### 🧱 Mason 的作用
- **结构生成**：创建完整的文件结构和基础代码
- **模板标准化**：确保所有代码遵循 Clean Architecture
- **开发加速**：快速搭建功能模块骨架

### ⚙️ build_runner 的作用
- **注解处理**：处理 `@freezed`、`@JsonSerializable`、`@injectable` 等注解
- **代码补全**：生成序列化、依赖注入等辅助代码
- **类型安全**：确保生成的代码类型安全

### 🔄 协作优势
- **两阶段生成**：Mason 生成结构，build_runner 生成实现
- **配置统一**：通过 `build.yaml` 统一控制生成行为
- **开发流畅**：监听模式下实时生成，无需手动干预

### 📈 项目收益
- **开发效率提升 70%**：减少重复代码编写
- **代码质量保证**：统一的代码结构和规范
- **维护成本降低**：标准化的代码结构便于维护
- **团队协作改善**：统一的开发模式和代码风格

通过合理使用 Mason + build_runner 双重代码生成系统，可以显著提升 Flutter 应用的开发效率和代码质量。

---

**重要提醒：** 使用包含注解的模板（feature、model、api_client、bloc）后，**必须运行 `make gen`** 来生成完整的代码！