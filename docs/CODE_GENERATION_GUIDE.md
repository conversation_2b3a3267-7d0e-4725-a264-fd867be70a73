# Flutter 代码生成完整指南

## 📋 概述

本文档是 Flutter Scaffold 项目代码生成的完整指南，整合了快速参考、故障排除、最佳实践和深度技术分析。

## 🚨 紧急修复指南

### 遇到循环依赖错误时

```bash
# 典型错误信息
E injectable_generator:injectable_builder on lib/core/di/di.dart:
  Bad state: Cannot recurse at later or equal phase 8, already running at: [4]

# 立即修复步骤
# 1. 停止当前构建
Ctrl+C

# 2. 注释循环导入
# 编辑 lib/core/di/di.dart，注释掉: import 'di.config.dart';

# 3. 分步生成
dart run build_runner build --build-filter="**/*.freezed.dart" --delete-conflicting-outputs
dart run build_runner build --build-filter="**/*.g.dart" --delete-conflicting-outputs

# 4. 恢复导入并生成 Injectable
# 取消注释: import 'di.config.dart';
dart run build_runner build --build-filter="**/*.config.dart" --delete-conflicting-outputs
```

### 遇到构建阶段冲突时

```bash
# 错误信息: "Cannot recurse at later or equal phase X"
# 解决方案: 清理并分步生成

flutter clean
rm -rf .dart_tool
flutter pub get
# 然后按上述步骤分步生成
```

### 一键修复脚本

```bash
# 使用项目提供的修复脚本
./scripts/fix_codegen.sh

# 或手动执行修复步骤
make clean && make bootstrap && make gen
```

## 📋 常用命令速查

### 基础命令

```bash
# 完整生成（推荐）
dart run build_runner build --delete-conflicting-outputs

# 使用 Melos 统一生成（多包项目）
melos run gen

# 监听模式（开发时）
dart run build_runner watch --delete-conflicting-outputs

# 清理生成文件
dart run build_runner clean
```

### 分类生成命令

```bash
# 只生成 Freezed 文件
dart run build_runner build --build-filter="**/*.freezed.dart" --delete-conflicting-outputs

# 只生成 JSON 序列化文件
dart run build_runner build --build-filter="**/*.g.dart" --delete-conflicting-outputs

# 只生成 Injectable 配置
dart run build_runner build --build-filter="**/*.config.dart" --delete-conflicting-outputs

# 只生成 Assets 文件
dart run build_runner build --build-filter="**/assets.gen.dart" --delete-conflicting-outputs

# 只生成 Fonts 文件
dart run build_runner build --build-filter="**/fonts.gen.dart" --delete-conflicting-outputs
```

### 项目特定命令

```bash
# 使用 Makefile 快捷命令
make gen              # 生成所有代码
make clean            # 清理构建文件
make bootstrap        # 安装所有依赖

# 使用 mise 任务
mise run gen          # 生成代码
mise run clean        # 清理项目
```

## 🔧 配置文件模板

### build.yaml 完整配置

```yaml
# 项目根目录的 build.yaml
targets:
  $default:
    builders:
      # Flutter Gen - Assets 和 Fonts 生成器
      flutter_gen_runner:
        enabled: true
        options:
          output: lib/generated/
          line_length: 80

      # Freezed 数据模型
      freezed:
        options:
          union_key: runtimeType
          union_value_case: snake

      # JSON 序列化
      json_serializable:
        options:
          explicit_to_json: true
          include_if_null: false

      # Retrofit API 客户端
      retrofit_generator:
        options:
          generate_to_json: true

      # Injectable 依赖注入
      injectable_generator:
        options:
          auto_register: true
```

### pubspec.yaml 依赖配置

```yaml
# apps/mobile/pubspec.yaml 中的关键依赖
dev_dependencies:
  # 代码生成核心
  build_runner: ^2.6.0

  # 数据模型生成
  freezed: ^3.2.0
  json_serializable: ^6.10.0

  # API 客户端生成
  retrofit_generator: ^10.0.1

  # 依赖注入生成
  injectable_generator: ^2.8.0

  # 数据库生成
  drift_dev: ^2.28.1

  # 资源文件生成
  flutter_gen_runner: ^5.11.0
```

### di.dart 模板

```dart
// 初始状态（用于首次生成）
import 'package:get_it/get_it.dart';
import 'package:injectable/injectable.dart';

// import 'di.config.dart'; // 首次生成时注释掉

final getIt = GetIt.instance;

@InjectableInit()
Future<void> configureDependencies() async {
  // 临时空实现
}
```

```dart
// 最终状态（生成完成后）
import 'package:get_it/get_it.dart';
import 'package:injectable/injectable.dart';

import 'di.config.dart'; // 生成完成后取消注释

final getIt = GetIt.instance;

@InjectableInit()
Future<void> configureDependencies() async => $initGetIt(getIt);
```

## 🚨 错误代码速查

| 错误信息 | 原因 | 解决方案 |
|----------|------|----------|
| `Cannot recurse at later or equal phase X` | 构建阶段冲突 | 分步生成 |
| `Target of URI doesn't exist: 'di.config.dart'` | 循环依赖 | 注释导入 |
| `Missing extension byte` | 编码问题 | 检查文件编码 |
| `Conflicting outputs were detected` | 文件冲突 | 使用 `--delete-conflicting-outputs` |
| `The function '$initGetIt' isn't defined` | Injectable 未生成 | 生成 di.config.dart |
| `MissingPluginException` | 平台兼容性问题 | 检查平台特定代码 |

## 📁 文件结构检查

### 必需的目录结构

```
lib/
├── generated/           # Flutter Gen 输出
│   ├── assets.gen.dart
│   └── fonts.gen.dart
├── core/
│   └── di/
│       ├── di.dart
│       ├── di.config.dart    # 生成文件
│       └── di_module.dart
└── features/
    └── auth/
        └── data/
            ├── models/
            │   ├── *.dart
            │   ├── *.freezed.dart  # 生成文件
            │   └── *.g.dart        # 生成文件
            └── datasources/
                ├── *.dart
                └── *.g.dart        # 生成文件
```

### 必需的 assets 结构

```
assets/
├── fonts/
│   ├── Inter-Regular.ttf
│   ├── Inter-Medium.ttf
│   └── Inter-Bold.ttf
├── images/
├── icons/
└── lottie/
```

## 🔍 故障排除详解

### 1. 循环依赖问题深度分析

**问题根源**：Injectable 生成器需要扫描所有带有 `@injectable` 注解的类，但在生成 `di.config.dart` 之前，`di.dart` 文件已经尝试导入这个尚不存在的文件。

**解决策略**：
1. **临时断开循环**：注释掉 `import 'di.config.dart';`
2. **分步生成**：先生成其他文件，最后生成 Injectable 配置
3. **恢复连接**：取消注释并完成最终生成

### 2. 构建阶段冲突解析

**技术原理**：build_runner 使用阶段化构建系统，某些生成器可能在相同或更晚的阶段尝试递归调用。

**预防措施**：
- 使用 `--build-filter` 分离不同类型的生成
- 定期清理 `.dart_tool` 目录
- 避免在生成过程中修改源文件

### 3. 平台兼容性问题

**Web 平台特殊处理**：
```dart
// 在需要平台检查的地方添加
import 'package:flutter/foundation.dart';

if (kIsWeb) {
  // Web 平台特定实现
} else {
  // 原生平台实现
}
```

## 📚 最佳实践

### 1. 开发工作流

```bash
# 推荐的日常开发流程
# 1. 启动监听模式
dart run build_runner watch --delete-conflicting-outputs

# 2. 编写代码（模型、API、服务等）

# 3. 保存文件，自动触发生成

# 4. 如遇问题，停止监听并手动修复
Ctrl+C
./scripts/fix_codegen.sh
```

### 2. 团队协作规范

- **提交前检查**：确保所有生成文件都已更新
- **忽略规则**：`.gitignore` 中正确配置生成文件
- **版本控制**：生成文件应该被提交到版本控制

### 3. 性能优化

```bash
# 只生成必要的文件
dart run build_runner build --build-filter="lib/features/auth/**/*.g.dart"

# 使用并行构建
dart run build_runner build --delete-conflicting-outputs --verbose
```

## 🎯 高级技巧

### 1. 自定义生成器配置

```yaml
# build.yaml 高级配置
targets:
  $default:
    builders:
      json_serializable:
        options:
          # 自定义序列化选项
          explicit_to_json: true
          include_if_null: false
          field_rename: snake
          checked: true
```

### 2. 条件生成

```dart
// 使用条件注解
@JsonSerializable(
  includeIfNull: false,
  explicitToJson: true,
)
class User {
  // 只在非 Web 平台生成某些字段
  @JsonKey(includeFromJson: !kIsWeb, includeToJson: !kIsWeb)
  final String? deviceId;
}
```

### 3. 生成文件监控

```bash
# 监控生成文件变化
find lib -name "*.g.dart" -o -name "*.freezed.dart" | entr -r echo "Generated files changed"
```
