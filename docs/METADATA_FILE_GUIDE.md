# Flutter .metadata 文件详细指南

## 📋 概述

`.metadata` 文件是 Flutter 项目的核心元数据配置文件，由 Flutter 工具链自动生成和维护。它记录了项目的版本信息、平台支持和迁移配置，确保项目在不同环境和版本间的一致性。

## 🔍 文件结构解析

### 完整文件示例
```yaml
# This file tracks properties of this Flutter project.
# Used by Flutter tool to assess capabilities and perform upgrades etc.
#
# This file should be version controlled and should not be manually edited.

version:
  revision: "d7b523b356d15fb81e7d340bbe52b47f93937323"
  channel: "stable"

project_type: app

# Tracks metadata for the flutter migrate command
migration:
  platforms:
    - platform: root
      create_revision: d7b523b356d15fb81e7d340bbe52b47f93937323
      base_revision: d7b523b356d15fb81e7d340bbe52b47f93937323
    - platform: android
      create_revision: d7b523b356d15fb81e7d340bbe52b47f93937323
      base_revision: d7b523b356d15fb81e7d340bbe52b47f93937323
    # ... 其他平台配置

  # User provided section
  unmanaged_files:
    - 'lib/main.dart'
    - 'ios/Runner.xcodeproj/project.pbxproj'
```

## 📊 字段详细说明

### 1. version 部分

#### revision
- **含义**: Flutter SDK 的具体 Git 提交哈希值
- **作用**: 确保团队使用完全相同的 Flutter 构建版本
- **示例**: `"d7b523b356d15fb81e7d340bbe52b47f93937323"`
- **查看方式**: 
  ```bash
  flutter --version
  # 输出: Framework • revision d7b523b356 (2 weeks ago)
  ```

#### channel
- **含义**: Flutter 发布渠道
- **可选值**: `stable`, `beta`, `dev`, `master`
- **推荐**: 生产项目使用 `stable`

### 2. project_type
- **含义**: 项目类型标识
- **常见值**: `app`, `package`, `plugin`
- **本项目**: `app` (应用程序)

### 3. migration 部分

#### platforms
记录每个支持平台的创建和基础版本信息：

| 平台 | 说明 | 用途 |
|------|------|------|
| `root` | 项目根配置 | 整体项目元数据 |
| `android` | Android 平台 | 移动端支持 |
| `ios` | iOS 平台 | 移动端支持 |
| `web` | Web 平台 | 浏览器支持 |
| `windows` | Windows 桌面 | 桌面端支持 |
| `macos` | macOS 桌面 | 桌面端支持 |
| `linux` | Linux 桌面 | 桌面端支持 |

#### unmanaged_files
**关键功能**: 保护自定义文件不被 Flutter 工具覆盖

**当前配置**:
```yaml
unmanaged_files:
  - 'lib/main.dart'                    # 保护自定义应用入口
  - 'ios/Runner.xcodeproj/project.pbxproj'  # 保护 iOS 项目配置
```

**保护原因**:
- `lib/main.dart`: 包含自定义的依赖注入、认证守卫等初始化逻辑
- `ios/Runner.xcodeproj/project.pbxproj`: 包含自定义的 iOS 构建配置

## 🎯 核心作用

### 1. 版本一致性保证
- 确保团队成员使用相同的 Flutter 版本
- 避免因版本差异导致的构建问题
- 支持 CI/CD 环境的版本复现

### 2. 迁移管理
- 记录项目的创建基线
- 指导 Flutter 版本升级时的迁移操作
- 保护自定义代码不被覆盖

### 3. 平台支持追踪
- 记录项目支持的所有平台
- 管理多平台项目的复杂性
- 支持平台的增量添加

## 🔧 相关命令

### 查看项目信息
```bash
# 查看当前 Flutter 版本
flutter --version

# 检查项目健康状态
flutter doctor

# 查看项目配置
flutter config
```

### 迁移相关
```bash
# 执行项目迁移
flutter migrate

# 添加新平台支持
flutter create --platforms web .
flutter create --platforms windows .
```

### 版本管理
```bash
# 升级 Flutter 版本
flutter upgrade

# 切换 Flutter 渠道
flutter channel stable
flutter channel beta
```

## ⚠️ 重要注意事项

### 1. 不要手动编辑
- ❌ **禁止手动修改**: 除了 `unmanaged_files` 部分
- ✅ **自动维护**: 由 Flutter 工具链管理
- ⚠️ **风险**: 手动修改可能导致工具链异常

### 2. 版本控制
- ✅ **必须提交**: 添加到 Git 版本控制
- ❌ **不要忽略**: 不应添加到 `.gitignore`
- 📝 **团队共享**: 确保所有成员获得相同配置

### 3. 备份建议
```bash
# 在重大操作前备份
cp apps/mobile/.metadata apps/mobile/.metadata.backup

# 如果出现问题，可以恢复
cp apps/mobile/.metadata.backup apps/mobile/.metadata
```

## 🚨 故障排除

### 常见问题

#### 1. 版本不匹配警告
```bash
# 解决方案：清理并重新获取依赖
flutter clean
flutter pub get
```

#### 2. 迁移失败
```bash
# 检查 .metadata 文件完整性
cat apps/mobile/.metadata

# 重新初始化（谨慎使用）
flutter create --platforms android,ios,web,windows,macos,linux .
```

#### 3. 平台支持异常
```bash
# 重新添加平台支持
flutter create --platforms [platform_name] .
```

### 恢复策略
如果文件损坏或丢失：
1. 从版本控制恢复
2. 重新运行 `flutter create`
3. 手动添加 `unmanaged_files` 配置

## 📚 最佳实践

### 1. 项目初始化
```bash
# 创建新项目时确保生成 .metadata
flutter create my_project
cd my_project
git add .metadata
git commit -m "Add Flutter metadata"
```

### 2. 团队协作
- 在项目 README 中说明 `.metadata` 的重要性
- 定期检查团队成员的 Flutter 版本一致性
- 在 CI/CD 中验证 Flutter 版本匹配

### 3. 版本升级
```bash
# 升级前备份
cp .metadata .metadata.backup

# 执行升级
flutter upgrade
flutter migrate

# 验证结果
flutter doctor
flutter analyze
```

## 🎯 本项目特殊配置

### 多环境支持
本项目使用多个入口文件，建议扩展 `unmanaged_files`：
```yaml
unmanaged_files:
  - 'lib/main.dart'
  - 'lib/main_development.dart'
  - 'lib/main_staging.dart'
  - 'lib/main_production.dart'
  - 'ios/Runner.xcodeproj/project.pbxproj'
  - 'android/app/build.gradle'
```

### 企业级配置
- 保护自定义的依赖注入配置
- 保护认证守卫初始化逻辑
- 保护多平台构建配置

## 📖 相关文档

- [Flutter 官方文档 - 项目结构](https://docs.flutter.dev/development/tools/sdk/overview)
- [Flutter 迁移指南](https://docs.flutter.dev/development/tools/sdk/release-notes)
- [项目配置文件说明](./PROJECT_CONFIGURATION_FILES.md)

---

**总结**: `.metadata` 文件是 Flutter 项目的重要基础设施，确保项目的版本一致性和迁移安全性。虽然看起来简单，但对于企业级多平台项目至关重要，应当妥善维护和保护。
