# Flutter Scaffold - Makefile
# Quick commands for development workflow

.PHONY: setup bootstrap clean test build run dev

# Environment variables
FLUTTER_VERSION ?= 3.32.5
APP_DIR := apps/mobile

# Setup development environment
setup:
	@echo "Setting up Flutter Scaffold development environment..."
	@echo "Installing Melos..."
	dart pub global activate melos
	@echo "Installing Mason..."
	dart pub global activate mason_cli
	@echo "Installing Patrol..."
	dart pub global activate patrol_cli
	@echo "Bootstrap packages..."
	melos bootstrap
	@echo "Setup complete! 🚀"

# Bootstrap all packages
bootstrap:
	melos bootstrap

# Clean all packages
clean:
	melos run clean

# Run tests for all packages
test:
	melos run test

# Run tests with coverage
test-coverage:
	melos run test --coverage

# Generate code for all packages
gen:
	melos run gen

# Format all packages
format:
	melos run format

# Analyze all packages
analyze:
	melos run analyze

# Run development server
run-dev:
	cd $(APP_DIR) && flutter run --flavor development -t lib/main_development.dart

# Run staging server
run-stag:
	cd $(APP_DIR) && flutter run --flavor staging -t lib/main_staging.dart

# Run production server
run-prod:
	cd $(APP_DIR) && flutter run --flavor production -t lib/main_production.dart

# Build APK for development
build-dev:
	cd $(APP_DIR) && flutter build apk --flavor development -t lib/main_development.dart

# Build APK for staging
build-stag:
	cd $(APP_DIR) && flutter build apk --flavor staging -t lib/main_staging.dart

# Build APK for production
build-prod:
	cd $(APP_DIR) && flutter build apk --flavor production -t lib/main_production.dart

# Build iOS for development
build-ios-dev:
	cd $(APP_DIR) && flutter build ios --flavor development -t lib/main_development.dart --no-codesign

# Build iOS for staging
build-ios-stag:
	cd $(APP_DIR) && flutter build ios --flavor staging -t lib/main_staging.dart --no-codesign

# Build iOS for production
build-ios-prod:
	cd $(APP_DIR) && flutter build ios --flavor production -t lib/main_production.dart --no-codesign

# Mason Templates - Code Generation
# Generate complete feature with all layers
feature-complete:
	@echo "🧱 Creating complete feature with all layers..."
	@read -p "Enter feature name (e.g., user_profile): " feature; \
	read -p "Enter entity name (e.g., User): " entity; \
	read -p "Enter API endpoint (e.g., /api/v1/users): " endpoint; \
	mason make feature_complete --feature $$feature --entity $$entity --api_endpoint $$endpoint

# Generate BLoC pattern
bloc:
	@echo "🧱 Creating BLoC pattern..."
	@read -p "Enter feature name: " feature; \
	read -p "Enter BLoC name: " name; \
	read -p "Enter entity name: " entity; \
	mason make bloc --feature $$feature --name $$name --entity $$entity

# Generate Repository pattern
repository:
	@echo "🧱 Creating Repository pattern..."
	@read -p "Enter feature name: " feature; \
	read -p "Enter repository name: " name; \
	read -p "Enter model name: " model; \
	mason make repository --feature $$feature --name $$name --model $$model

# Generate UseCase pattern
usecase:
	@echo "🧱 Creating UseCase pattern..."
	@read -p "Enter feature name: " feature; \
	read -p "Enter usecase name: " name; \
	read -p "Enter repository name: " repository; \
	read -p "Enter entity name: " entity; \
	mason make usecase --feature $$feature --name $$name --repository $$repository --entity $$entity

# Generate legacy feature (backward compatibility)
feature:
	@echo "🧱 Creating legacy feature..."
	@read -p "Enter feature name: " name; \
	mason make feature --name $$name

# Generate new ADR
adr:
	@echo "📝 Creating Architecture Decision Record..."
	@read -p "Enter ADR title: " title; \
	mason make adr --name "$$title"

# List all available Mason templates
mason-list:
	@echo "🧱 Available Mason templates:"
	@mason list

# Get Mason template info
mason-info:
	@read -p "Enter template name: " template; \
	mason info $$template

# Update dependencies
upgrade:
	melos run upgrade

# Check for outdated packages
outdated:
	melos exec -- flutter pub outdated

# Run integration tests
integration:
	cd $(APP_DIR) && patrol test integration_test

# Help
help:
	@echo "🚀 Flutter Scaffold - Available Commands:"
	@echo ""
	@echo "📦 Environment & Dependencies:"
	@echo "  setup           - Setup development environment"
	@echo "  bootstrap       - Bootstrap all packages"
	@echo "  clean           - Clean all packages"
	@echo "  upgrade         - Update dependencies"
	@echo "  outdated        - Check for outdated packages"
	@echo ""
	@echo "🧪 Testing & Quality:"
	@echo "  test            - Run tests for all packages"
	@echo "  test-coverage   - Run tests with coverage"
	@echo "  integration     - Run integration tests"
	@echo "  analyze         - Analyze all packages"
	@echo "  format          - Format all packages"
	@echo ""
	@echo "🏗️  Build & Run:"
	@echo "  run-dev         - Run development server"
	@echo "  run-stag        - Run staging server"
	@echo "  run-prod        - Run production server"
	@echo "  build-dev       - Build APK for development"
	@echo "  build-stag      - Build APK for staging"
	@echo "  build-prod      - Build APK for production"
	@echo "  build-ios-dev   - Build iOS for development"
	@echo "  build-ios-stag  - Build iOS for staging"
	@echo "  build-ios-prod  - Build iOS for production"
	@echo ""
	@echo "🧱 Code Generation (Mason Templates):"
	@echo "  feature-complete - Generate complete feature with all layers"
	@echo "  bloc            - Generate BLoC pattern"
	@echo "  repository      - Generate Repository pattern"
	@echo "  usecase         - Generate UseCase pattern"
	@echo "  feature         - Generate legacy feature (backward compatibility)"
	@echo "  adr             - Generate Architecture Decision Record"
	@echo "  mason-list      - List all available Mason templates"
	@echo "  mason-info      - Get Mason template information"
	@echo ""
	@echo "🔧 Other:"
	@echo "  gen             - Generate code for all packages"