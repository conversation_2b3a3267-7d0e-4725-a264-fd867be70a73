{"adr": "E:/project/flutter/flutter_scaffold/bricks/adr", "api_client": "E:/project/flutter/flutter_scaffold/bricks/api_client", "bloc": "E:/project/flutter/flutter_scaffold/bricks/bloc", "data_source": "E:/project/flutter/flutter_scaffold/bricks/data_source", "feature": "E:/project/flutter/flutter_scaffold/bricks/feature", "feature_complete": "E:/project/flutter/flutter_scaffold/bricks/feature_complete", "model": "E:/project/flutter/flutter_scaffold/bricks/model", "page": "E:/project/flutter/flutter_scaffold/bricks/page", "repository": "E:/project/flutter/flutter_scaffold/bricks/repository", "service": "E:/project/flutter/flutter_scaffold/bricks/service", "test": "E:/project/flutter/flutter_scaffold/bricks/test", "usecase": "E:/project/flutter/flutter_scaffold/bricks/usecase", "validator": "E:/project/flutter/flutter_scaffold/bricks/validator", "widget": "E:/project/flutter/flutter_scaffold/bricks/widget"}