{"bricks": {"adr": {"path": "E:/project/flutter/flutter_scaffold/bricks/adr"}, "api_client": {"path": "E:/project/flutter/flutter_scaffold/bricks/api_client"}, "bloc": {"path": "E:/project/flutter/flutter_scaffold/bricks/bloc"}, "data_source": {"path": "E:/project/flutter/flutter_scaffold/bricks/data_source"}, "feature": {"path": "E:/project/flutter/flutter_scaffold/bricks/feature"}, "feature_complete": {"path": "E:/project/flutter/flutter_scaffold/bricks/feature_complete"}, "model": {"path": "E:/project/flutter/flutter_scaffold/bricks/model"}, "page": {"path": "E:/project/flutter/flutter_scaffold/bricks/page"}, "repository": {"path": "E:/project/flutter/flutter_scaffold/bricks/repository"}, "service": {"path": "E:/project/flutter/flutter_scaffold/bricks/service"}, "test": {"path": "E:/project/flutter/flutter_scaffold/bricks/test"}, "usecase": {"path": "E:/project/flutter/flutter_scaffold/bricks/usecase"}, "validator": {"path": "E:/project/flutter/flutter_scaffold/bricks/validator"}, "widget": {"path": "E:/project/flutter/flutter_scaffold/bricks/widget"}}}