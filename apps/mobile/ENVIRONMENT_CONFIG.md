# 环境配置管理

本项目使用 `flutter_dotenv` 进行环境变量管理，支持多环境配置。

## 📁 配置文件结构

```
apps/mobile/
├── .env.development    # 开发环境配置
├── .env.staging       # 测试环境配置
├── .env.production    # 生产环境配置
└── .env.example       # 配置模板文件
```

## 🚀 快速开始

### 1. 复制配置模板
```bash
cd apps/mobile
cp .env.example .env.development
cp .env.example .env.staging
cp .env.example .env.production
```

### 2. 配置各环境参数
编辑对应的 `.env.*` 文件，填入正确的配置值。

### 3. 运行应用
```bash
# 开发环境
flutter run -t lib/main_development.dart --dart-define=ENVIRONMENT=development

# 测试环境
flutter run -t lib/main_staging.dart --dart-define=ENVIRONMENT=staging

# 生产环境
flutter run -t lib/main_production.dart --dart-define=ENVIRONMENT=production
```

## ⚙️ 配置参数说明

### API 配置
- `API_BASE_URL`: API 服务器地址
- `API_VERSION`: API 版本号
- `WEBSOCKET_URL`: WebSocket 服务器地址

### 网络配置
- `CONNECT_TIMEOUT`: 连接超时时间（秒）
- `RECEIVE_TIMEOUT`: 接收超时时间（秒）
- `SEND_TIMEOUT`: 发送超时时间（秒）

### 功能开关
- `ENABLE_LOGGING`: 是否启用日志记录
- `ENABLE_DEBUG_TOOLS`: 是否启用调试工具
- `ENABLE_ANALYTICS`: 是否启用数据分析
- `ENABLE_CRASH_REPORTING`: 是否启用崩溃报告

### Firebase 配置
- `FIREBASE_API_KEY`: Firebase API 密钥
- `FIREBASE_APP_ID`: Firebase 应用 ID
- `FIREBASE_PROJECT_ID`: Firebase 项目 ID

## 🔒 安全注意事项

### 1. 敏感信息保护
- ❌ **绝不要**将真实的生产环境密钥提交到版本控制
- ✅ 使用 CI/CD 环境变量或密钥管理服务
- ✅ 生产环境配置文件中使用占位符

### 2. 版本控制
- `.env.*` 文件已在 `.gitignore` 中排除
- 只提交 `.env.example` 模板文件
- 团队成员需要自行配置本地环境文件

### 3. CI/CD 集成
```yaml
# GitHub Actions 示例
- name: Create environment file
  run: |
    echo "API_BASE_URL=${{ secrets.API_BASE_URL }}" >> .env.production
    echo "FIREBASE_API_KEY=${{ secrets.FIREBASE_API_KEY }}" >> .env.production
```

## 🛠️ 开发指南

### 添加新的配置项
1. 在 `.env.example` 中添加新参数
2. 在 `EnvironmentConfig` 类中添加对应的 getter
3. 在 `ConfigValidator` 中添加验证逻辑
4. 更新各环境的配置文件

### 配置验证
应用启动时会自动验证配置的完整性和正确性：
- 必需参数检查
- 格式验证
- 生产环境特殊检查

### 调试配置
开发和测试环境会在启动时打印配置摘要，方便调试。

## 📋 最佳实践

1. **环境隔离**: 不同环境使用不同的 API 地址和配置
2. **功能开关**: 使用配置控制功能的启用/禁用
3. **性能调优**: 根据环境调整超时和缓存参数
4. **安全第一**: 生产环境关闭调试功能和日志
5. **配置验证**: 启动时验证配置的正确性

## 🔧 故障排除

### 配置未生效
1. 检查 `.env.*` 文件是否存在
2. 确认 `ENVIRONMENT` 参数是否正确传递
3. 查看启动日志中的配置验证信息

### 构建失败
1. 确认 `pubspec.yaml` 中包含了 `.env.*` 文件
2. 检查配置文件格式是否正确
3. 验证所有必需参数是否已配置
