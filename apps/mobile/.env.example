# Environment Configuration Template
# Copy this file to .env.development, .env.staging, or .env.production
# and fill in the appropriate values for each environment

# API Configuration
API_BASE_URL=https://api.example.com
API_VERSION=v1
WEBSOCKET_URL=wss://ws.example.com

# Network Configuration (in seconds)
CONNECT_TIMEOUT=10
RECEIVE_TIMEOUT=15
SEND_TIMEOUT=10

# Feature Flags (true/false)
ENABLE_LOGGING=false
ENABLE_DEBUG_TOOLS=false
ENABLE_ANALYTICS=true
ENABLE_CRASH_REPORTING=true

# Security Configuration
# SECURITY: Never commit real production keys to version control
ENCRYPTION_KEY=your_encryption_key_here
APP_SECRET_KEY=your_app_secret_key_here
ENABLE_BIOMETRIC=true

# Database Configuration
DATABASE_NAME=app.db
ENABLE_DATABASE_LOGGING=false

# Cache Configuration
CACHE_EXPIRY_MINUTES=30
MAX_CACHE_SIZE_MB=100

# Mock/Test Configuration
ENABLE_MOCK_DATA=false
MOCK_DELAY_MS=0
