import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:mocktail/mocktail.dart';
import 'package:get_it/get_it.dart';

import 'package:flutter_scaffold_mobile/features/auth/presentation/bloc/auth_bloc.dart';
import 'package:flutter_scaffold_mobile/features/auth/domain/repositories/auth_repository.dart';
import 'package:flutter_scaffold_mobile/core/theme/theme_bloc.dart';
import 'package:flutter_scaffold_mobile/core/theme/theme_manager.dart';
import 'package:flutter_scaffold_mobile/core/storage/secure_storage_service.dart';
import 'package:flutter_scaffold_mobile/core/network/dio_client.dart';

// Mock classes
class MockAuthBloc extends Mock implements AuthBloc {
  final _completer = Completer<void>();

  @override
  Future<void> close() {
    if (!_completer.isCompleted) {
      _completer.complete();
    }
    return _completer.future;
  }
}

class MockAuthRepository extends Mock implements AuthRepository {}

class MockThemeBloc extends Mock implements ThemeBloc {
  final _completer = Completer<void>();

  @override
  Future<void> close() {
    if (!_completer.isCompleted) {
      _completer.complete();
    }
    return _completer.future;
  }
}

class MockThemeManager extends Mock implements ThemeManager {}

class MockSecureStorageService extends Mock implements SecureStorageService {}

class MockDioClient extends Mock implements DioClient {}

class MockBuildContext extends Mock implements BuildContext {}

/// Test helper to create a widget with proper providers and ignore layout overflow errors
Widget createTestWidget(Widget child, {MockAuthBloc? authBloc}) {
  final mockAuthBloc = authBloc ?? MockAuthBloc();

  return MaterialApp(
    home: BlocProvider<AuthBloc>(
      create: (_) => mockAuthBloc,
      child: child,
    ),
  );
}

/// Pump widget and ignore layout overflow errors
Future<void> pumpWidgetWithoutOverflowErrors(
  WidgetTester tester,
  Widget widget,
) async {
  // Ignore overflow errors during testing
  FlutterError.onError = (FlutterErrorDetails details) {
    final exception = details.exception;
    final isOverflowError = exception is FlutterError &&
        exception.message.contains('overflowed by');

    if (!isOverflowError) {
      FlutterError.presentError(details);
    }
  };

  await tester.pumpWidget(widget);

  // Restore error handling
  FlutterError.onError = FlutterError.presentError;
}

/// 测试辅助工具类
class TestHelpers {
  TestHelpers._();

  /// 设置测试环境
  static Future<void> setupTestEnvironment() async {
    // 重置GetIt实例
    if (GetIt.instance.isRegistered<ThemeManager>()) {
      await GetIt.instance.reset();
    }

    // 注册Mock服务
    GetIt.instance.registerLazySingleton<SecureStorageService>(
      () => MockSecureStorageService(),
    );

    GetIt.instance.registerLazySingleton<DioClient>(
      () => MockDioClient(),
    );

    GetIt.instance.registerLazySingleton<AuthRepository>(
      () => MockAuthRepository(),
    );

    GetIt.instance.registerLazySingleton<ThemeManager>(
      () => MockThemeManager(),
    );

    GetIt.instance.registerFactory<ThemeBloc>(
      () => MockThemeBloc(),
    );

    GetIt.instance.registerFactory<AuthBloc>(
      () => MockAuthBloc(),
    );
  }

  /// 清理测试环境
  static Future<void> tearDownTestEnvironment() async {
    await GetIt.instance.reset();
  }

  /// 创建测试用的MaterialApp
  static Widget createTestApp({
    required Widget child,
    List<BlocProvider>? providers,
    ThemeData? theme,
    Locale? locale,
  }) {
    return MultiBlocProvider(
      providers: providers ?? [
        BlocProvider<ThemeBloc>(
          create: (_) => MockThemeBloc(),
        ),
        BlocProvider<AuthBloc>(
          create: (_) => MockAuthBloc(),
        ),
      ],
      child: MaterialApp(
        theme: theme,
        locale: locale,
        home: child,
        debugShowCheckedModeBanner: false,
      ),
    );
  }

  /// 等待动画完成
  static Future<void> pumpAndSettle(WidgetTester tester, [Duration? duration]) async {
    await tester.pumpAndSettle(duration ?? const Duration(milliseconds: 100));
  }

  /// 查找文本组件
  static Finder findText(String text) => find.text(text);

  /// 查找图标组件
  static Finder findIcon(IconData icon) => find.byIcon(icon);

  /// 查找按钮组件
  static Finder findButton(String text) => find.widgetWithText(ElevatedButton, text);

  /// 查找文本按钮
  static Finder findTextButton(String text) => find.widgetWithText(TextButton, text);

  /// 输入文本到输入框
  static Future<void> enterText(
    WidgetTester tester,
    Finder finder,
    String text,
  ) async {
    await tester.enterText(finder, text);
    await tester.pump();
  }

  /// 点击组件
  static Future<void> tap(WidgetTester tester, Finder finder) async {
    await tester.tap(finder);
    await tester.pump();
  }

  /// 验证组件存在
  static void expectToFind(Finder finder, [int count = 1]) {
    expect(finder, findsNWidgets(count));
  }

  /// 验证组件不存在
  static void expectNotToFind(Finder finder) {
    expect(finder, findsNothing);
  }

  /// 验证文本存在
  static void expectText(String text, [int count = 1]) {
    expectToFind(findText(text), count);
  }

  /// 创建Mock用户数据
  static Map<String, dynamic> createMockUserData({
    String id = '1',
    String name = '测试用户',
    String email = '<EMAIL>',
  }) {
    return {
      'id': id,
      'name': name,
      'email': email,
      'createdAt': DateTime.now().toIso8601String(),
    };
  }
}

/// Setup mock AuthBloc with default behavior
void setupMockAuthBloc(MockAuthBloc mockAuthBloc) {
  when(() => mockAuthBloc.state).thenReturn(const AuthState());
  when(() => mockAuthBloc.stream).thenAnswer((_) => Stream.value(const AuthState()));
}
