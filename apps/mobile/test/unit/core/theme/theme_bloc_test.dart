import 'package:bloc_test/bloc_test.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:flutter/material.dart';

import 'package:flutter_scaffold_mobile/core/theme/theme_bloc.dart';
import 'package:flutter_scaffold_mobile/core/theme/theme_manager.dart';
import 'package:flutter_scaffold_mobile/core/theme/theme_config.dart';
import 'package:flutter_scaffold_mobile/core/theme/custom_themes.dart';

class MockThemeManager extends Mock implements ThemeManager {}

void main() {
  group('ThemeBloc', () {
    late ThemeBloc themeBloc;
    late MockThemeManager mockThemeManager;

    setUp(() {
      mockThemeManager = MockThemeManager();

      // Setup default mock behavior
      when(() => mockThemeManager.currentThemeConfig).thenReturn(
        const ThemeConfig(
          appTheme: AppTheme.system,
          themeMode: ThemeMode.system,
        ),
      );
      when(() => mockThemeManager.getLightTheme()).thenReturn(CustomThemes.defaultLight);
      when(() => mockThemeManager.getDarkTheme()).thenReturn(CustomThemes.defaultDark);
      when(() => mockThemeManager.themeNotifier).thenReturn(
        ValueNotifier(const ThemeConfig(
          appTheme: AppTheme.system,
          themeMode: ThemeMode.system,
        )),
      );

      themeBloc = ThemeBloc(mockThemeManager);
    });

    tearDown(() {
      themeBloc.close();
    });

    test('initial state has system theme config', () {
      expect(themeBloc.state.config.appTheme, AppTheme.system);
      expect(themeBloc.state.config.themeMode, ThemeMode.system);
    });

    blocTest<ThemeBloc, ThemeState>(
      'emits loading then success when ThemeInitialized is added',
      build: () {
        when(() => mockThemeManager.initialize()).thenAnswer((_) async {});
        return ThemeBloc(mockThemeManager);
      },
      act: (bloc) => bloc.add(const ThemeInitialized()),
      expect: () => [
        predicate<ThemeState>((state) => state.isLoading == true),
        predicate<ThemeState>((state) => state.isLoading == false),
      ],
      verify: (_) {
        verify(() => mockThemeManager.initialize()).called(1);
      },
    );

    blocTest<ThemeBloc, ThemeState>(
      'emits new theme when ThemeChanged is added',
      build: () {
        when(() => mockThemeManager.setTheme(any())).thenAnswer((_) async {});
        return ThemeBloc(mockThemeManager);
      },
      act: (bloc) => bloc.add(const ThemeChanged(AppTheme.businessBlue)),
      verify: (_) {
        verify(() => mockThemeManager.setTheme(AppTheme.businessBlue)).called(1);
      },
    );

    blocTest<ThemeBloc, ThemeState>(
      'emits error state when theme initialization fails',
      build: () {
        when(() => mockThemeManager.initialize()).thenThrow(Exception('Init failed'));
        return ThemeBloc(mockThemeManager);
      },
      act: (bloc) => bloc.add(const ThemeInitialized()),
      expect: () => [
        predicate<ThemeState>((state) => state.isLoading == true),
        predicate<ThemeState>((state) =>
          state.isLoading == false &&
          state.errorMessage != null &&
          state.errorMessage!.contains('Failed to initialize theme')
        ),
      ],
    );
  });
}