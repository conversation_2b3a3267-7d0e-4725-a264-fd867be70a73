import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'theme_bloc.dart';
import 'theme_config.dart';
import 'theme_extensions.dart';

class ThemeSwitcher extends StatelessWidget {
  const ThemeSwitcher({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ThemeBloc, ThemeState>(
      builder: (context, state) {
        return PopupMenuButton<AppTheme>(
          icon: Icon(state.config.appTheme.icon),
          tooltip: '切换主题',
          onSelected: (appTheme) {
            context.read<ThemeBloc>().add(ThemeChanged(appTheme));
          },
          itemBuilder: (context) => AppTheme.values.map((theme) {
            return PopupMenuItem<AppTheme>(
              value: theme,
              child: Row(
                children: [
                  Icon(theme.icon),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          theme.displayName,
                          style: context.textStyles.body,
                        ),
                        if (theme.description.isNotEmpty)
                          Text(
                            theme.description,
                            style: context.textStyles.caption?.copyWith(
                              color: context.colors.onSurfaceVariant,
                            ),
                          ),
                      ],
                    ),
                  ),
                  if (state.config.appTheme == theme)
                    Icon(
                      Icons.check,
                      color: context.colors.primary,
                      size: 20,
                    ),
                ],
              ),
            );
          }).toList(),
        );
      },
    );
  }
}