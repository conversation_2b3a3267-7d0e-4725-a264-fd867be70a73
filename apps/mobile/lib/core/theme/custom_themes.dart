import 'package:flutter/material.dart';
import 'package:flex_color_scheme/flex_color_scheme.dart';

/// 自定义主题集合
class CustomThemes {
  const CustomThemes._();

  /// 默认浅色主题
  static ThemeData get defaultLight => FlexThemeData.light(
        colors: const FlexSchemeColor(
          primary: Color(0xFF2A6BFF),
          primaryContainer: Color(0xFFD0E4FF),
          secondary: Color(0xFF006875),
          secondaryContainer: Color(0xFF95F0FF),
          tertiary: Color(0xFF006874),
          tertiaryContainer: Color(0xFF97F0FF),
          appBarColor: Color(0xFF95F0FF),
          error: Color(0xFFBA1A1A),
        ),
        surfaceMode: FlexSurfaceMode.levelSurfacesLowScaffold,
        blendLevel: 7,
        subThemesData: _defaultSubThemes,
        visualDensity: FlexColorScheme.comfortablePlatformDensity,
        useMaterial3: true,
        swapLegacyOnMaterial3: true,
        fontFamily: 'Inter',
      );

  /// 默认深色主题
  static ThemeData get defaultDark => FlexThemeData.dark(
        colors: const FlexSchemeColor(
          primary: Color(0xFFAEC6FF),
          primaryContainer: Color(0xFF00497C),
          secondary: Color(0xFF4FD8EB),
          secondaryContainer: Color(0xFF004E59),
          tertiary: Color(0xFF4FD8EB),
          tertiaryContainer: Color(0xFF004F5A),
          appBarColor: Color(0xFF004E59),
          error: Color(0xFFFFB4AB),
        ),
        surfaceMode: FlexSurfaceMode.levelSurfacesLowScaffold,
        blendLevel: 13,
        subThemesData: _defaultSubThemes.copyWith(blendOnLevel: 20),
        visualDensity: FlexColorScheme.comfortablePlatformDensity,
        useMaterial3: true,
        swapLegacyOnMaterial3: true,
        fontFamily: 'Inter',
      );

  /// 商务蓝色主题
  static ThemeData get businessBlue => FlexThemeData.light(
        colors: const FlexSchemeColor(
          primary: Color(0xFF1565C0),
          primaryContainer: Color(0xFFBBDEFB),
          secondary: Color(0xFF0277BD),
          secondaryContainer: Color(0xFFB3E5FC),
          tertiary: Color(0xFF01579B),
          tertiaryContainer: Color(0xFFE1F5FE),
          appBarColor: Color(0xFFB3E5FC),
          error: Color(0xFFD32F2F),
        ),
        surfaceMode: FlexSurfaceMode.levelSurfacesLowScaffold,
        blendLevel: 5,
        subThemesData: _defaultSubThemes,
        visualDensity: FlexColorScheme.comfortablePlatformDensity,
        useMaterial3: true,
        fontFamily: 'Inter',
      );

  /// 商务蓝色深色主题
  static ThemeData get businessBlueDark => FlexThemeData.dark(
        colors: const FlexSchemeColor(
          primary: Color(0xFF90CAF9),
          primaryContainer: Color(0xFF0D47A1),
          secondary: Color(0xFF81D4FA),
          secondaryContainer: Color(0xFF01579B),
          tertiary: Color(0xFFB3E5FC),
          tertiaryContainer: Color(0xFF003C71),
          appBarColor: Color(0xFF01579B),
          error: Color(0xFFEF5350),
        ),
        surfaceMode: FlexSurfaceMode.levelSurfacesLowScaffold,
        blendLevel: 15,
        subThemesData: _defaultSubThemes.copyWith(blendOnLevel: 20),
        visualDensity: FlexColorScheme.comfortablePlatformDensity,
        useMaterial3: true,
        fontFamily: 'Inter',
      );

  /// 自然绿色主题
  static ThemeData get natureGreen => FlexThemeData.light(
        colors: const FlexSchemeColor(
          primary: Color(0xFF2E7D32),
          primaryContainer: Color(0xFFC8E6C9),
          secondary: Color(0xFF388E3C),
          secondaryContainer: Color(0xFFDCEDC8),
          tertiary: Color(0xFF1B5E20),
          tertiaryContainer: Color(0xFFE8F5E8),
          appBarColor: Color(0xFFDCEDC8),
          error: Color(0xFFD32F2F),
        ),
        surfaceMode: FlexSurfaceMode.levelSurfacesLowScaffold,
        blendLevel: 6,
        subThemesData: _defaultSubThemes,
        visualDensity: FlexColorScheme.comfortablePlatformDensity,
        useMaterial3: true,
        fontFamily: 'Inter',
      );

  /// 自然绿色深色主题
  static ThemeData get natureGreenDark => FlexThemeData.dark(
        colors: const FlexSchemeColor(
          primary: Color(0xFF81C784),
          primaryContainer: Color(0xFF1B5E20),
          secondary: Color(0xFFA5D6A7),
          secondaryContainer: Color(0xFF2E7D32),
          tertiary: Color(0xFFC8E6C9),
          tertiaryContainer: Color(0xFF1B5E20),
          appBarColor: Color(0xFF2E7D32),
          error: Color(0xFFEF5350),
        ),
        surfaceMode: FlexSurfaceMode.levelSurfacesLowScaffold,
        blendLevel: 15,
        subThemesData: _defaultSubThemes.copyWith(blendOnLevel: 20),
        visualDensity: FlexColorScheme.comfortablePlatformDensity,
        useMaterial3: true,
        fontFamily: 'Inter',
      );

  /// 温暖橙色主题
  static ThemeData get warmOrange => FlexThemeData.light(
        colors: const FlexSchemeColor(
          primary: Color(0xFFE65100),
          primaryContainer: Color(0xFFFFCCBC),
          secondary: Color(0xFFFF6F00),
          secondaryContainer: Color(0xFFFFE0B2),
          tertiary: Color(0xFFBF360C),
          tertiaryContainer: Color(0xFFFFF3E0),
          appBarColor: Color(0xFFFFE0B2),
          error: Color(0xFFD32F2F),
        ),
        surfaceMode: FlexSurfaceMode.levelSurfacesLowScaffold,
        blendLevel: 6,
        subThemesData: _defaultSubThemes,
        visualDensity: FlexColorScheme.comfortablePlatformDensity,
        useMaterial3: true,
        fontFamily: 'Inter',
      );

  /// 温暖橙色深色主题
  static ThemeData get warmOrangeDark => FlexThemeData.dark(
        colors: const FlexSchemeColor(
          primary: Color(0xFFFFAB91),
          primaryContainer: Color(0xFFBF360C),
          secondary: Color(0xFFFFCC02),
          secondaryContainer: Color(0xFFE65100),
          tertiary: Color(0xFFFFE0B2),
          tertiaryContainer: Color(0xFFBF360C),
          appBarColor: Color(0xFFE65100),
          error: Color(0xFFEF5350),
        ),
        surfaceMode: FlexSurfaceMode.levelSurfacesLowScaffold,
        blendLevel: 15,
        subThemesData: _defaultSubThemes.copyWith(blendOnLevel: 20),
        visualDensity: FlexColorScheme.comfortablePlatformDensity,
        useMaterial3: true,
        fontFamily: 'Inter',
      );

  /// 优雅紫色主题
  static ThemeData get elegantPurple => FlexThemeData.light(
        colors: const FlexSchemeColor(
          primary: Color(0xFF6A1B9A),
          primaryContainer: Color(0xFFE1BEE7),
          secondary: Color(0xFF8E24AA),
          secondaryContainer: Color(0xFFF3E5F5),
          tertiary: Color(0xFF4A148C),
          tertiaryContainer: Color(0xFFF8BBD9),
          appBarColor: Color(0xFFF3E5F5),
          error: Color(0xFFD32F2F),
        ),
        surfaceMode: FlexSurfaceMode.levelSurfacesLowScaffold,
        blendLevel: 6,
        subThemesData: _defaultSubThemes,
        visualDensity: FlexColorScheme.comfortablePlatformDensity,
        useMaterial3: true,
        fontFamily: 'Inter',
      );

  /// 优雅紫色深色主题
  static ThemeData get elegantPurpleDark => FlexThemeData.dark(
        colors: const FlexSchemeColor(
          primary: Color(0xFFCE93D8),
          primaryContainer: Color(0xFF4A148C),
          secondary: Color(0xFFE1BEE7),
          secondaryContainer: Color(0xFF6A1B9A),
          tertiary: Color(0xFFF8BBD9),
          tertiaryContainer: Color(0xFF4A148C),
          appBarColor: Color(0xFF6A1B9A),
          error: Color(0xFFEF5350),
        ),
        surfaceMode: FlexSurfaceMode.levelSurfacesLowScaffold,
        blendLevel: 15,
        subThemesData: _defaultSubThemes.copyWith(blendOnLevel: 20),
        visualDensity: FlexColorScheme.comfortablePlatformDensity,
        useMaterial3: true,
        fontFamily: 'Inter',
      );

  /// 经典黑白主题
  static ThemeData get classicMono => FlexThemeData.light(
        colors: const FlexSchemeColor(
          primary: Color(0xFF212121),
          primaryContainer: Color(0xFFE0E0E0),
          secondary: Color(0xFF424242),
          secondaryContainer: Color(0xFFF5F5F5),
          tertiary: Color(0xFF616161),
          tertiaryContainer: Color(0xFFEEEEEE),
          appBarColor: Color(0xFFF5F5F5),
          error: Color(0xFFD32F2F),
        ),
        surfaceMode: FlexSurfaceMode.levelSurfacesLowScaffold,
        blendLevel: 2,
        subThemesData: _defaultSubThemes,
        visualDensity: FlexColorScheme.comfortablePlatformDensity,
        useMaterial3: true,
        fontFamily: 'Inter',
      );

  /// 经典黑白深色主题
  static ThemeData get classicMonoDark => FlexThemeData.dark(
        colors: const FlexSchemeColor(
          primary: Color(0xFFE0E0E0),
          primaryContainer: Color(0xFF212121),
          secondary: Color(0xFFBDBDBD),
          secondaryContainer: Color(0xFF424242),
          tertiary: Color(0xFF9E9E9E),
          tertiaryContainer: Color(0xFF616161),
          appBarColor: Color(0xFF424242),
          error: Color(0xFFEF5350),
        ),
        surfaceMode: FlexSurfaceMode.levelSurfacesLowScaffold,
        blendLevel: 5,
        subThemesData: _defaultSubThemes.copyWith(blendOnLevel: 10),
        visualDensity: FlexColorScheme.comfortablePlatformDensity,
        useMaterial3: true,
        fontFamily: 'Inter',
      );

  /// 从ColorScheme创建主题
  static ThemeData fromColorScheme(ColorScheme colorScheme, {bool isDark = false}) {
    return isDark
        ? FlexThemeData.dark(
            colorScheme: colorScheme,
            surfaceMode: FlexSurfaceMode.levelSurfacesLowScaffold,
            blendLevel: 15,
            subThemesData: _defaultSubThemes.copyWith(blendOnLevel: 20),
            visualDensity: FlexColorScheme.comfortablePlatformDensity,
            useMaterial3: true,
            fontFamily: 'Inter',
          )
        : FlexThemeData.light(
            colorScheme: colorScheme,
            surfaceMode: FlexSurfaceMode.levelSurfacesLowScaffold,
            blendLevel: 7,
            subThemesData: _defaultSubThemes,
            visualDensity: FlexColorScheme.comfortablePlatformDensity,
            useMaterial3: true,
            fontFamily: 'Inter',
          );
  }

  /// 从自定义颜色创建主题
  static ThemeData fromCustomColors(Map<String, Color> colors, {bool isDark = false}) {
    final primary = colors['primary'] ?? Colors.blue;
    final secondary = colors['secondary'] ?? Colors.teal;

    final flexColors = FlexSchemeColor(
      primary: primary,
      primaryContainer: isDark ? primary.withValues(alpha: 0.3) : primary.withValues(alpha: 0.1),
      secondary: secondary,
      secondaryContainer: isDark ? secondary.withValues(alpha: 0.3) : secondary.withValues(alpha: 0.1),
      tertiary: colors['tertiary'] ?? (isDark ? Colors.purple.shade300 : Colors.purple),
      tertiaryContainer: isDark ? Colors.purple.withValues(alpha: 0.3) : Colors.purple.withValues(alpha: 0.1),
      appBarColor: isDark ? secondary.withValues(alpha: 0.3) : secondary.withValues(alpha: 0.1),
      error: isDark ? Colors.red.shade300 : Colors.red,
    );

    return isDark
        ? FlexThemeData.dark(
            colors: flexColors,
            surfaceMode: FlexSurfaceMode.levelSurfacesLowScaffold,
            blendLevel: 15,
            subThemesData: _defaultSubThemes.copyWith(blendOnLevel: 20),
            visualDensity: FlexColorScheme.comfortablePlatformDensity,
            useMaterial3: true,
            fontFamily: 'Inter',
          )
        : FlexThemeData.light(
            colors: flexColors,
            surfaceMode: FlexSurfaceMode.levelSurfacesLowScaffold,
            blendLevel: 7,
            subThemesData: _defaultSubThemes,
            visualDensity: FlexColorScheme.comfortablePlatformDensity,
            useMaterial3: true,
            fontFamily: 'Inter',
          );
  }

  /// 默认子主题配置
  static const FlexSubThemesData _defaultSubThemes = FlexSubThemesData(
    blendOnLevel: 10,
    blendOnColors: false,
    useMaterial3Typography: true,
    useM2StyleDividerInM3: true,
    alignedDropdown: true,
    useInputDecoratorThemeInDialogs: true,
    inputDecoratorBorderType: FlexInputBorderType.outline,
    inputDecoratorRadius: 12.0,
    fabUseShape: true,
    fabRadius: 16.0,
    chipRadius: 8.0,
    cardRadius: 12.0,
    popupMenuRadius: 8.0,
    dialogRadius: 16.0,
    timePickerDialogRadius: 16.0,
    snackBarRadius: 8.0,
    appBarScrolledUnderElevation: 1.0,
    bottomSheetRadius: 16.0,
    bottomNavigationBarElevation: 0.0,
    navigationBarHeight: 80.0,
    navigationBarLabelBehavior: NavigationDestinationLabelBehavior.alwaysShow,
  );
}
