import 'package:flutter/material.dart';
import 'package:equatable/equatable.dart';

/// 应用主题枚举
enum AppTheme {
  system,           // 跟随系统
  light,            // 浅色主题
  dark,             // 深色主题
  materialYou,      // Material You动态主题
  businessBlue,     // 商务蓝色主题
  natureGreen,      // 自然绿色主题
  warmOrange,       // 温暖橙色主题
  elegantPurple,    // 优雅紫色主题
  classicMono,      // 经典黑白主题
  custom,           // 用户自定义主题
}

/// 主题配置类
class ThemeConfig extends Equatable {
  /// 当前应用主题
  final AppTheme appTheme;
  
  /// Flutter主题模式
  final ThemeMode themeMode;
  
  /// 是否支持动态颜色
  final bool supportsDynamicColor;
  
  /// 是否有自定义颜色
  final bool hasCustomColors;
  
  const ThemeConfig({
    required this.appTheme,
    required this.themeMode,
    this.supportsDynamicColor = false,
    this.hasCustomColors = false,
  });
  
  /// 复制并修改配置
  ThemeConfig copyWith({
    AppTheme? appTheme,
    ThemeMode? themeMode,
    bool? supportsDynamicColor,
    bool? hasCustomColors,
  }) {
    return ThemeConfig(
      appTheme: appTheme ?? this.appTheme,
      themeMode: themeMode ?? this.themeMode,
      supportsDynamicColor: supportsDynamicColor ?? this.supportsDynamicColor,
      hasCustomColors: hasCustomColors ?? this.hasCustomColors,
    );
  }
  
  @override
  List<Object?> get props => [
        appTheme,
        themeMode,
        supportsDynamicColor,
        hasCustomColors,
      ];
}

/// 主题扩展方法
extension AppThemeExtension on AppTheme {
  /// 主题显示名称
  String get displayName {
    switch (this) {
      case AppTheme.system:
        return '跟随系统';
      case AppTheme.light:
        return '浅色主题';
      case AppTheme.dark:
        return '深色主题';
      case AppTheme.materialYou:
        return 'Material You';
      case AppTheme.businessBlue:
        return '商务蓝';
      case AppTheme.natureGreen:
        return '自然绿';
      case AppTheme.warmOrange:
        return '温暖橙';
      case AppTheme.elegantPurple:
        return '优雅紫';
      case AppTheme.classicMono:
        return '经典黑白';
      case AppTheme.custom:
        return '自定义';
    }
  }
  
  /// 主题描述
  String get description {
    switch (this) {
      case AppTheme.system:
        return '自动跟随系统设置';
      case AppTheme.light:
        return '明亮清爽的浅色界面';
      case AppTheme.dark:
        return '护眼舒适的深色界面';
      case AppTheme.materialYou:
        return '基于壁纸的动态配色';
      case AppTheme.businessBlue:
        return '专业稳重的商务风格';
      case AppTheme.natureGreen:
        return '清新自然的绿色调';
      case AppTheme.warmOrange:
        return '温暖活力的橙色调';
      case AppTheme.elegantPurple:
        return '优雅神秘的紫色调';
      case AppTheme.classicMono:
        return '简约经典的黑白风格';
      case AppTheme.custom:
        return '个性化自定义配色';
    }
  }
  
  /// 主题图标
  IconData get icon {
    switch (this) {
      case AppTheme.system:
        return Icons.brightness_auto;
      case AppTheme.light:
        return Icons.brightness_high;
      case AppTheme.dark:
        return Icons.brightness_low;
      case AppTheme.materialYou:
        return Icons.palette;
      case AppTheme.businessBlue:
        return Icons.business;
      case AppTheme.natureGreen:
        return Icons.eco;
      case AppTheme.warmOrange:
        return Icons.wb_sunny;
      case AppTheme.elegantPurple:
        return Icons.auto_awesome;
      case AppTheme.classicMono:
        return Icons.contrast;
      case AppTheme.custom:
        return Icons.color_lens;
    }
  }
  
  /// 是否为深色主题
  bool get isDark {
    switch (this) {
      case AppTheme.dark:
        return true;
      case AppTheme.system:
      case AppTheme.materialYou:
        // 这些主题跟随系统，需要运行时判断
        return false;
      default:
        return false;
    }
  }
  
  /// 是否为浅色主题
  bool get isLight {
    switch (this) {
      case AppTheme.light:
      case AppTheme.businessBlue:
      case AppTheme.natureGreen:
      case AppTheme.warmOrange:
      case AppTheme.elegantPurple:
      case AppTheme.classicMono:
      case AppTheme.custom:
        return true;
      default:
        return false;
    }
  }
  
  /// 是否为系统主题
  bool get isSystem {
    return this == AppTheme.system || this == AppTheme.materialYou;
  }
  
  /// 是否需要动态颜色支持
  bool get requiresDynamicColor {
    return this == AppTheme.materialYou;
  }
  
  /// 是否为自定义主题
  bool get isCustom {
    return this == AppTheme.custom;
  }
  
  /// 主题预览颜色
  Color get previewColor {
    switch (this) {
      case AppTheme.system:
        return Colors.grey;
      case AppTheme.light:
        return Colors.blue;
      case AppTheme.dark:
        return Colors.blueGrey;
      case AppTheme.materialYou:
        return Colors.deepPurple;
      case AppTheme.businessBlue:
        return const Color(0xFF1565C0);
      case AppTheme.natureGreen:
        return const Color(0xFF2E7D32);
      case AppTheme.warmOrange:
        return const Color(0xFFE65100);
      case AppTheme.elegantPurple:
        return const Color(0xFF6A1B9A);
      case AppTheme.classicMono:
        return Colors.black87;
      case AppTheme.custom:
        return Colors.pink;
    }
  }
}

/// 主题模式扩展
extension ThemeModeExtension on ThemeMode {
  /// 显示名称
  String get displayName {
    switch (this) {
      case ThemeMode.system:
        return '跟随系统';
      case ThemeMode.light:
        return '浅色模式';
      case ThemeMode.dark:
        return '深色模式';
    }
  }
  
  /// 图标
  IconData get icon {
    switch (this) {
      case ThemeMode.system:
        return Icons.brightness_auto;
      case ThemeMode.light:
        return Icons.brightness_high;
      case ThemeMode.dark:
        return Icons.brightness_low;
    }
  }
}
