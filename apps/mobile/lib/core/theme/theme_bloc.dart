import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:injectable/injectable.dart';
import 'package:equatable/equatable.dart';

import 'theme_manager.dart';
import 'theme_config.dart';

// Theme Events
abstract class ThemeEvent extends Equatable {
  const ThemeEvent();

  @override
  List<Object?> get props => [];
}

class ThemeInitialized extends ThemeEvent {
  const ThemeInitialized();
}

class ThemeChanged extends ThemeEvent {
  final AppTheme appTheme;

  const ThemeChanged(this.appTheme);

  @override
  List<Object?> get props => [appTheme];
}

class CustomColorsChanged extends ThemeEvent {
  final Map<String, Color> colors;

  const CustomColorsChanged(this.colors);

  @override
  List<Object?> get props => [colors];
}

// Theme States
class ThemeState extends Equatable {
  final ThemeConfig config;
  final ThemeData lightTheme;
  final ThemeData darkTheme;
  final bool isLoading;
  final String? errorMessage;

  const ThemeState({
    required this.config,
    required this.lightTheme,
    required this.darkTheme,
    this.isLoading = false,
    this.errorMessage,
  });

  ThemeState copyWith({
    ThemeConfig? config,
    ThemeData? lightTheme,
    ThemeData? darkTheme,
    bool? isLoading,
    String? errorMessage,
  }) {
    return ThemeState(
      config: config ?? this.config,
      lightTheme: lightTheme ?? this.lightTheme,
      darkTheme: darkTheme ?? this.darkTheme,
      isLoading: isLoading ?? this.isLoading,
      errorMessage: errorMessage,
    );
  }

  @override
  List<Object?> get props => [
        config,
        lightTheme,
        darkTheme,
        isLoading,
        errorMessage,
      ];
}

// Theme Bloc
@injectable
class ThemeBloc extends Bloc<ThemeEvent, ThemeState> {
  final ThemeManager _themeManager;

  ThemeBloc(this._themeManager) : super(ThemeState(
    config: const ThemeConfig(
      appTheme: AppTheme.system,
      themeMode: ThemeMode.system,
    ),
    lightTheme: _themeManager.getLightTheme(),
    darkTheme: _themeManager.getDarkTheme(),
  )) {
    on<ThemeInitialized>(_onThemeInitialized);
    on<ThemeChanged>(_onThemeChanged);
    on<CustomColorsChanged>(_onCustomColorsChanged);

    // 监听主题管理器的变更
    _themeManager.themeNotifier.addListener(_onThemeManagerChanged);
  }

  Future<void> _onThemeInitialized(
    ThemeInitialized event,
    Emitter<ThemeState> emit,
  ) async {
    emit(state.copyWith(isLoading: true));

    try {
      await _themeManager.initialize();
      _updateStateFromManager(emit);
    } catch (e) {
      emit(state.copyWith(
        isLoading: false,
        errorMessage: 'Failed to initialize theme: $e',
      ));
    }
  }

  Future<void> _onThemeChanged(
    ThemeChanged event,
    Emitter<ThemeState> emit,
  ) async {
    try {
      await _themeManager.setTheme(event.appTheme);
      _updateStateFromManager(emit);
    } catch (e) {
      emit(state.copyWith(
        errorMessage: 'Failed to change theme: $e',
      ));
    }
  }

  Future<void> _onCustomColorsChanged(
    CustomColorsChanged event,
    Emitter<ThemeState> emit,
  ) async {
    try {
      await _themeManager.setCustomColors(event.colors);
      _updateStateFromManager(emit);
    } catch (e) {
      emit(state.copyWith(
        errorMessage: 'Failed to set custom colors: $e',
      ));
    }
  }

  void _onThemeManagerChanged() {
    // 不能在这里直接使用emit，需要通过事件触发
    add(const ThemeInitialized());
  }

  void _updateStateFromManager(Emitter<ThemeState> emit) {
    emit(state.copyWith(
      config: _themeManager.currentThemeConfig,
      lightTheme: _themeManager.getLightTheme(),
      darkTheme: _themeManager.getDarkTheme(),
      isLoading: false,
      errorMessage: null,
    ));
  }

  @override
  Future<void> close() {
    _themeManager.themeNotifier.removeListener(_onThemeManagerChanged);
    return super.close();
  }
}