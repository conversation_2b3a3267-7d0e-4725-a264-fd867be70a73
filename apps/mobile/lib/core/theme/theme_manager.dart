import 'package:flutter/material.dart';
import 'package:injectable/injectable.dart';
import 'package:dynamic_color/dynamic_color.dart';
import 'package:flutter/foundation.dart';

import '../storage/secure_storage_service.dart';
import '../logging/app_logger.dart';
import 'theme_config.dart';
import 'custom_themes.dart';

/// 主题管理器
///
/// 负责管理应用的主题状态，包括：
/// - Material You 动态颜色支持
/// - 自定义主题方案
/// - 主题持久化存储
/// - 主题切换通知
@lazySingleton
class ThemeManager {
  static const String _themeKey = 'app_theme';
  static const String _customColorsKey = 'custom_colors';

  final SecureStorageService _storage;

  // 当前主题配置
  AppTheme _currentTheme = AppTheme.system;
  ColorScheme? _dynamicLightColorScheme;
  ColorScheme? _dynamicDarkColorScheme;
  Map<String, Color>? _customColors;

  // 主题变更通知
  final ValueNotifier<ThemeConfig> _themeNotifier = ValueNotifier(
    const ThemeConfig(
      appTheme: AppTheme.system,
      themeMode: ThemeMode.system,
    ),
  );

  ThemeManager(this._storage);

  /// 主题变更通知器
  ValueListenable<ThemeConfig> get themeNotifier => _themeNotifier;

  /// 当前主题配置
  ThemeConfig get currentThemeConfig => _themeNotifier.value;

  /// 当前应用主题
  AppTheme get currentTheme => _currentTheme;

  /// 动态颜色方案（浅色）
  ColorScheme? get dynamicLightColorScheme => _dynamicLightColorScheme;

  /// 动态颜色方案（深色）
  ColorScheme? get dynamicDarkColorScheme => _dynamicDarkColorScheme;

  /// 是否支持动态颜色
  bool get supportsDynamicColor =>
      _dynamicLightColorScheme != null && _dynamicDarkColorScheme != null;

  /// 初始化主题管理器
  Future<void> initialize() async {
    try {
      appLogger.info('Initializing ThemeManager...');

      // 加载保存的主题设置
      await _loadSavedTheme();

      // 初始化动态颜色
      await _initializeDynamicColors();

      // 加载自定义颜色
      await _loadCustomColors();

      // 更新主题配置
      _updateThemeConfig();

      appLogger.info('ThemeManager initialized successfully');
    } catch (e) {
      appLogger.error('Failed to initialize ThemeManager', e);
      // 使用默认主题
      _currentTheme = AppTheme.system;
      _updateThemeConfig();
    }
  }

  /// 切换主题
  Future<void> setTheme(AppTheme theme) async {
    if (_currentTheme == theme) return;

    try {
      _currentTheme = theme;
      await _saveTheme();
      _updateThemeConfig();

      appLogger.info('Theme changed to: ${theme.name}');
    } catch (e) {
      appLogger.error('Failed to set theme', e);
    }
  }

  /// 设置自定义颜色
  Future<void> setCustomColors(Map<String, Color> colors) async {
    try {
      _customColors = Map.from(colors);
      await _saveCustomColors();

      if (_currentTheme == AppTheme.custom) {
        _updateThemeConfig();
      }

      appLogger.info('Custom colors updated');
    } catch (e) {
      appLogger.error('Failed to set custom colors', e);
    }
  }

  /// 获取浅色主题数据
  ThemeData getLightTheme() {
    switch (_currentTheme) {
      case AppTheme.system:
      case AppTheme.light:
        return CustomThemes.defaultLight;

      case AppTheme.materialYou:
        if (_dynamicLightColorScheme != null) {
          return CustomThemes.fromColorScheme(_dynamicLightColorScheme!);
        }
        return CustomThemes.defaultLight;

      case AppTheme.businessBlue:
        return CustomThemes.businessBlue;

      case AppTheme.natureGreen:
        return CustomThemes.natureGreen;

      case AppTheme.warmOrange:
        return CustomThemes.warmOrange;

      case AppTheme.elegantPurple:
        return CustomThemes.elegantPurple;

      case AppTheme.classicMono:
        return CustomThemes.classicMono;

      case AppTheme.custom:
        if (_customColors != null) {
          return CustomThemes.fromCustomColors(_customColors!);
        }
        return CustomThemes.defaultLight;

      case AppTheme.dark:
        return CustomThemes.defaultDark;
    }
  }

  /// 获取深色主题数据
  ThemeData getDarkTheme() {
    switch (_currentTheme) {
      case AppTheme.system:
      case AppTheme.dark:
        return CustomThemes.defaultDark;

      case AppTheme.materialYou:
        if (_dynamicDarkColorScheme != null) {
          return CustomThemes.fromColorScheme(_dynamicDarkColorScheme!, isDark: true);
        }
        return CustomThemes.defaultDark;

      case AppTheme.businessBlue:
        return CustomThemes.businessBlueDark;

      case AppTheme.natureGreen:
        return CustomThemes.natureGreenDark;

      case AppTheme.warmOrange:
        return CustomThemes.warmOrangeDark;

      case AppTheme.elegantPurple:
        return CustomThemes.elegantPurpleDark;

      case AppTheme.classicMono:
        return CustomThemes.classicMonoDark;

      case AppTheme.custom:
        if (_customColors != null) {
          return CustomThemes.fromCustomColors(_customColors!, isDark: true);
        }
        return CustomThemes.defaultDark;

      case AppTheme.light:
        return CustomThemes.defaultLight;
    }
  }

  /// 获取主题模式
  ThemeMode getThemeMode() {
    switch (_currentTheme) {
      case AppTheme.system:
      case AppTheme.materialYou:
        return ThemeMode.system;
      case AppTheme.light:
      case AppTheme.businessBlue:
      case AppTheme.natureGreen:
      case AppTheme.warmOrange:
      case AppTheme.elegantPurple:
      case AppTheme.classicMono:
      case AppTheme.custom:
        return ThemeMode.light;
      case AppTheme.dark:
        return ThemeMode.dark;
    }
  }

  /// 加载保存的主题
  Future<void> _loadSavedTheme() async {
    try {
      final themeString = await _storage.getSecureConfig(_themeKey);
      if (themeString != null) {
        final themeIndex = int.tryParse(themeString);
        if (themeIndex != null && themeIndex < AppTheme.values.length) {
          _currentTheme = AppTheme.values[themeIndex];
        }
      }
    } catch (e) {
      appLogger.warning('Failed to load saved theme: $e');
    }
  }

  /// 保存主题设置
  Future<void> _saveTheme() async {
    try {
      await _storage.storeSecureConfig(_themeKey, _currentTheme.index.toString());
    } catch (e) {
      appLogger.warning('Failed to save theme: $e');
    }
  }

  /// 初始化动态颜色
  Future<void> _initializeDynamicColors() async {
    if (kIsWeb) {
      // Web平台暂不支持动态颜色
      appLogger.info('Dynamic colors not supported on Web platform');
      return;
    }

    try {
      final corePalette = await DynamicColorPlugin.getCorePalette();
      if (corePalette != null) {
        _dynamicLightColorScheme = corePalette.toColorScheme();
        _dynamicDarkColorScheme = corePalette.toColorScheme(brightness: Brightness.dark);
        appLogger.info('Dynamic colors initialized successfully');
      } else {
        appLogger.info('Dynamic colors not available on this device');
      }
    } catch (e) {
      appLogger.warning('Failed to initialize dynamic colors: $e');
    }
  }

  /// 加载自定义颜色
  Future<void> _loadCustomColors() async {
    try {
      final colorsString = await _storage.getSecureConfig(_customColorsKey);
      if (colorsString != null) {
        // 这里可以实现颜色的序列化/反序列化
        // 暂时使用简单的实现
        appLogger.info('Custom colors loaded');
      }
    } catch (e) {
      appLogger.warning('Failed to load custom colors: $e');
    }
  }

  /// 保存自定义颜色
  Future<void> _saveCustomColors() async {
    if (_customColors != null) {
      try {
        // 这里可以实现颜色的序列化
        // 暂时使用简单的实现
        await _storage.storeSecureConfig(_customColorsKey, 'custom_colors_data');
        appLogger.info('Custom colors saved');
      } catch (e) {
        appLogger.warning('Failed to save custom colors: $e');
      }
    }
  }

  /// 更新主题配置
  void _updateThemeConfig() {
    final config = ThemeConfig(
      appTheme: _currentTheme,
      themeMode: getThemeMode(),
      supportsDynamicColor: supportsDynamicColor,
      hasCustomColors: _customColors != null,
    );

    _themeNotifier.value = config;
  }

  /// 释放资源
  void dispose() {
    _themeNotifier.dispose();
  }
}
