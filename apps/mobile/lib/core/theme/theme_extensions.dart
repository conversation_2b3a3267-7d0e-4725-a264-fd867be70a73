import 'package:flutter/material.dart';

/// 主题扩展工具类
///
/// 提供便捷的主题访问方法，统一使用 Material 3 ColorScheme
/// 避免直接使用废弃的 primaryColor 等属性
extension ThemeExtensions on ThemeData {
  /// 获取主要颜色 - 替代废弃的 primaryColor
  Color get primaryColor => colorScheme.primary;

  /// 获取主要颜色的容器色
  Color get primaryContainer => colorScheme.primaryContainer;

  /// 获取次要颜色 - 替代废弃的 accentColor
  Color get secondaryColor => colorScheme.secondary;

  /// 获取次要颜色的容器色
  Color get secondaryContainer => colorScheme.secondaryContainer;

  /// 获取表面颜色 - 替代废弃的 backgroundColor
  Color get surfaceColor => colorScheme.surface;

  /// 获取背景颜色
  Color get backgroundColor => colorScheme.surface;

  /// 获取错误颜色
  Color get errorColor => colorScheme.error;

  /// 获取在主要颜色上的文字颜色
  Color get onPrimaryColor => colorScheme.onPrimary;

  /// 获取在次要颜色上的文字颜色
  Color get onSecondaryColor => colorScheme.onSecondary;

  /// 获取在表面上的文字颜色
  Color get onSurfaceColor => colorScheme.onSurface;

  /// 获取在背景上的文字颜色
  Color get onBackgroundColor => colorScheme.onSurface;
}

/// 颜色扩展工具类
///
/// 提供高性能的颜色透明度操作，替代 withOpacity()
extension ColorExtensions on Color {
  /// 高性能透明度设置 - 替代 withOpacity()
  ///
  /// 使用 Color.fromARGB 避免运行时计算
  Color withAlpha(double opacity) {
    assert(opacity >= 0.0 && opacity <= 1.0);
    return Color.fromARGB(
      (255 * opacity).round(),
      (r * 255.0).round() & 0xff,
      (g * 255.0).round() & 0xff,
      (b * 255.0).round() & 0xff,
    );
  }

  /// 语义化透明度方法
  Color get subtle => withAlpha(0.05);      // 极淡 - 用于大面积背景
  Color get light => withAlpha(0.1);        // 淡 - 用于卡片背景
  Color get medium => withAlpha(0.3);       // 中等 - 用于边框
  Color get strong => withAlpha(0.7);       // 强 - 用于图标
  Color get intense => withAlpha(0.9);      // 极强 - 用于重要文本

  /// 交互状态颜色 - 符合 Material Design 规范
  Color get disabled => withAlpha(0.38);    // 禁用状态
  Color get hover => withAlpha(0.08);       // 悬停状态
  Color get pressed => withAlpha(0.12);     // 按压状态
  Color get focus => withAlpha(0.12);       // 焦点状态
  Color get selected => withAlpha(0.16);    // 选中状态
  Color get dragged => withAlpha(0.16);     // 拖拽状态

  /// 常用透明度预设 - 保持向后兼容
  Color get withLowOpacity => light;
  Color get withMediumOpacity => medium;
  Color get withHighOpacity => strong;
  Color get withVeryHighOpacity => intense;

  /// 颜色变化方法
  Color get lighter => Color.lerp(this, Colors.white, 0.3) ?? this;
  Color get darker => Color.lerp(this, Colors.black, 0.3) ?? this;
  Color get lightest => Color.lerp(this, Colors.white, 0.6) ?? this;
  Color get darkest => Color.lerp(this, Colors.black, 0.6) ?? this;

  /// 获取对比色（用于文本）
  Color get onColor {
    // 计算亮度，选择合适的对比色
    final luminance = computeLuminance();
    return luminance > 0.5 ? Colors.black87 : Colors.white;
  }

  /// 获取高对比度文本颜色
  Color get onColorHighContrast {
    final luminance = computeLuminance();
    return luminance > 0.5 ? Colors.black : Colors.white;
  }
}

/// 主题颜色预设
///
/// 提供常用的颜色组合，避免重复的透明度计算
class ThemeColors {
  const ThemeColors._();

  /// 根据主题获取预设颜色
  static ThemeColorSet of(BuildContext context) {
    final theme = context.theme;
    return ThemeColorSet._(theme);
  }
}

/// 主题颜色集合
class ThemeColorSet {
  const ThemeColorSet._(this._theme);

  final ThemeData _theme;

  /// 获取当前是否为暗色主题
  bool get _isDark => _theme.brightness == Brightness.dark;

  /// 主要颜色相关
  Color get primary => _theme.colorScheme.primary;
  Color get primaryContainer => _theme.colorScheme.primaryContainer;
  Color get onPrimary => _theme.colorScheme.onPrimary;
  Color get onPrimaryContainer => _theme.colorScheme.onPrimaryContainer;
  Color get primaryLight => primary.light;
  Color get primaryMedium => primary.medium;
  Color get primaryShadow => primary.medium;
  Color get primaryGradientStart => primary;
  Color get primaryGradientEnd => primary.strong;

  /// 次要颜色相关
  Color get secondary => _theme.colorScheme.secondary;
  Color get secondaryContainer => _theme.colorScheme.secondaryContainer;
  Color get onSecondary => _theme.colorScheme.onSecondary;
  Color get onSecondaryContainer => _theme.colorScheme.onSecondaryContainer;
  Color get secondaryLight => secondary.light;
  Color get secondaryMedium => secondary.medium;

  /// 表面颜色相关
  Color get surface => _theme.colorScheme.surface;
  Color get surfaceVariant => _theme.colorScheme.surfaceContainerHighest;
  Color get surfaceContainer => _theme.colorScheme.surfaceContainer;
  Color get surfaceContainerHigh => _theme.colorScheme.surfaceContainerHigh;
  Color get surfaceContainerHighest => _theme.colorScheme.surfaceContainerHighest;
  Color get onSurface => _theme.colorScheme.onSurface;
  Color get onSurfaceVariant => _theme.colorScheme.onSurfaceVariant;

  /// 语义化状态颜色 - 自动适配亮暗主题
  Color get success => _isDark ? Colors.green.shade400 : Colors.green.shade600;
  Color get successLight => _isDark
      ? Colors.green.shade900.withAlpha((255 * 0.3).round())
      : Colors.green.shade50;
  Color get successContainer => _isDark
      ? Colors.green.shade800
      : Colors.green.shade100;

  Color get warning => _isDark ? Colors.orange.shade400 : Colors.orange.shade600;
  Color get warningLight => _isDark
      ? Colors.orange.shade900.withAlpha((255 * 0.3).round())
      : Colors.orange.shade50;
  Color get warningContainer => _isDark
      ? Colors.orange.shade800
      : Colors.orange.shade100;

  Color get danger => _theme.colorScheme.error;
  Color get dangerLight => _theme.colorScheme.errorContainer;
  Color get dangerContainer => _theme.colorScheme.errorContainer;

  Color get info => _isDark ? Colors.blue.shade400 : Colors.blue.shade600;
  Color get infoLight => _isDark
      ? Colors.blue.shade900.withAlpha((255 * 0.3).round())
      : Colors.blue.shade50;
  Color get infoContainer => _isDark
      ? Colors.blue.shade800
      : Colors.blue.shade100;

  /// 完整中性色系统
  Color get neutral50 => _isDark ? Colors.grey.shade900 : Colors.grey.shade50;
  Color get neutral100 => _isDark ? Colors.grey.shade800 : Colors.grey.shade100;
  Color get neutral200 => _isDark ? Colors.grey.shade700 : Colors.grey.shade200;
  Color get neutral300 => _isDark ? Colors.grey.shade600 : Colors.grey.shade300;
  Color get neutral400 => _isDark ? Colors.grey.shade500 : Colors.grey.shade400;
  Color get neutral500 => _isDark ? Colors.grey.shade400 : Colors.grey.shade500;
  Color get neutral600 => _isDark ? Colors.grey.shade300 : Colors.grey.shade600;
  Color get neutral700 => _isDark ? Colors.grey.shade200 : Colors.grey.shade700;
  Color get neutral800 => _isDark ? Colors.grey.shade100 : Colors.grey.shade800;
  Color get neutral900 => _isDark ? Colors.grey.shade50 : Colors.grey.shade900;

  /// 阴影颜色
  Color get shadow => Colors.black.light;
  Color get shadowMedium => Colors.black.withAlpha((255 * 0.15).round());
  Color get shadowStrong => Colors.black.medium;

  /// 边框颜色
  Color get border => _theme.colorScheme.outline;
  Color get borderLight => _theme.colorScheme.outline.medium;
  Color get borderStrong => _theme.colorScheme.outline;

  /// 功能性颜色
  Color get divider => _theme.dividerColor;
  Color get disabled => _theme.disabledColor;
  Color get hint => _theme.hintColor;
  Color get focus => _theme.focusColor;
  Color get hover => _theme.hoverColor;
  Color get splash => _theme.splashColor;
  Color get highlight => _theme.highlightColor;

  /// 向后兼容的属性名
  Color get error => danger;
}

/// 上下文扩展工具类
///
/// 提供便捷的主题访问方法，简化代码
extension ContextExtensions on BuildContext {
  /// 快速访问主题
  ThemeData get theme => Theme.of(this);
  ColorScheme get colors => theme.colorScheme;
  TextTheme get textStyles => theme.textTheme;

  /// 快速访问自定义扩展
  ThemeColorSet get themeColors => ThemeColors.of(this);

  /// 响应式设计辅助
  bool get isDarkMode => theme.brightness == Brightness.dark;
  bool get isLightMode => !isDarkMode;

  /// 屏幕尺寸辅助
  Size get screenSize => MediaQuery.of(this).size;
  double get screenWidth => screenSize.width;
  double get screenHeight => screenSize.height;

  /// 安全区域辅助
  EdgeInsets get padding => MediaQuery.of(this).padding;
  EdgeInsets get viewInsets => MediaQuery.of(this).viewInsets;
  EdgeInsets get viewPadding => MediaQuery.of(this).viewPadding;

  /// 设备信息辅助
  double get devicePixelRatio => MediaQuery.of(this).devicePixelRatio;
  Orientation get orientation => MediaQuery.of(this).orientation;
  bool get isPortrait => orientation == Orientation.portrait;
  bool get isLandscape => orientation == Orientation.landscape;

  /// 响应式断点辅助
  bool get isMobile => screenWidth < 600;
  bool get isTablet => screenWidth >= 600 && screenWidth < 1200;
  bool get isDesktop => screenWidth >= 1200;

  /// 导航辅助
  NavigatorState get navigator => Navigator.of(this);
  ScaffoldMessengerState get scaffoldMessenger => ScaffoldMessenger.of(this);
  ScaffoldState? get scaffold => Scaffold.maybeOf(this);
}

/// 文本样式扩展
///
/// 提供便捷的文本样式访问，确保使用正确的 Material 3 命名
extension TextThemeExtensions on TextTheme {
  /// 大标题样式 - 替代废弃的 headline1
  TextStyle? get displayLargeStyle => displayLarge;

  /// 中标题样式 - 替代废弃的 headline2
  TextStyle? get displayMediumStyle => displayMedium;

  /// 小标题样式 - 替代废弃的 headline3
  TextStyle? get displaySmallStyle => displaySmall;

  /// 页面标题样式 - 替代废弃的 headline6
  TextStyle? get titleLargeStyle => titleLarge;

  /// 卡片标题样式 - 替代废弃的 subtitle1
  TextStyle? get titleMediumStyle => titleMedium;

  /// 小标题样式 - 替代废弃的 subtitle2
  TextStyle? get titleSmallStyle => titleSmall;

  /// 正文大样式 - 替代废弃的 bodyText1
  TextStyle? get bodyLargeStyle => bodyLarge;

  /// 正文样式 - 替代废弃的 bodyText2
  TextStyle? get bodyMediumStyle => bodyMedium;

  /// 说明文字样式 - 替代废弃的 caption
  TextStyle? get bodySmallStyle => bodySmall;

  /// 按钮文字样式 - 替代废弃的 button
  TextStyle? get labelLargeStyle => labelLarge;

  /// 小标签样式 - 替代废弃的 overline
  TextStyle? get labelSmallStyle => labelSmall;

  /// 语义化标题样式
  TextStyle? get heading1 => displayLarge?.copyWith(
    fontWeight: FontWeight.bold,
    letterSpacing: -0.5,
    height: 1.2,
  );

  TextStyle? get heading2 => displayMedium?.copyWith(
    fontWeight: FontWeight.bold,
    letterSpacing: -0.25,
    height: 1.3,
  );

  TextStyle? get heading3 => displaySmall?.copyWith(
    fontWeight: FontWeight.w600,
    height: 1.3,
  );

  TextStyle? get heading4 => headlineMedium?.copyWith(
    fontWeight: FontWeight.w600,
    height: 1.4,
  );

  TextStyle? get heading5 => headlineSmall?.copyWith(
    fontWeight: FontWeight.w500,
    height: 1.4,
  );

  TextStyle? get heading6 => titleLarge?.copyWith(
    fontWeight: FontWeight.w500,
    height: 1.5,
  );

  /// 语义化正文样式
  TextStyle? get subtitle => titleMedium;
  TextStyle? get body => bodyMedium;
  TextStyle? get caption => bodySmall?.copyWith(
    color: Colors.grey.shade600,
    height: 1.4,
  );

  /// 功能性样式
  TextStyle? get button => labelLarge?.copyWith(
    fontWeight: FontWeight.w500,
    letterSpacing: 0.5,
  );

  TextStyle? get link => bodyMedium?.copyWith(
    color: Colors.blue,
    decoration: TextDecoration.underline,
  );

  TextStyle? get code => bodyMedium?.copyWith(
    fontFamily: 'monospace',
    backgroundColor: Colors.grey.shade100,
    fontSize: (bodyMedium?.fontSize ?? 14) * 0.9,
  );

  TextStyle? get error => bodyMedium?.copyWith(
    color: Colors.red.shade600,
  );

  TextStyle? get success => bodyMedium?.copyWith(
    color: Colors.green.shade600,
  );

  TextStyle? get warning => bodyMedium?.copyWith(
    color: Colors.orange.shade600,
  );

  TextStyle? get info => bodyMedium?.copyWith(
    color: Colors.blue.shade600,
  );
}
