import '../config/environment_config.dart';

class AppConstants {
  static const String appName = 'Flutter Scaffold';

  // API - 使用环境变量配置
  static String get baseUrl => EnvironmentConfig.apiBaseUrl;
  static String get apiVersion => EnvironmentConfig.apiVersion;
  // 动态超时配置，根据环境优化
  static Duration get connectTimeout => EnvironmentConfig.connectTimeout;
  static Duration get receiveTimeout => EnvironmentConfig.receiveTimeout;
  static Duration get sendTimeout => EnvironmentConfig.sendTimeout;

  // Storage
  static const String userBox = 'user_box';
  static const String settingsBox = 'settings_box';

  // Routes
  static const String initialRoute = '/';
  static const String loginRoute = '/login';
  static const String homeRoute = '/home';
  static const String profileRoute = '/profile';
  static const String themeSelectionRoute = '/theme-selection';

  // Keys
  static const String accessTokenKey = 'access_token';
  static const String authTokenKey = 'auth_token'; // Keep for backward compatibility
  static const String refreshTokenKey = 'refresh_token';
  static const String userIdKey = 'user_id';
}