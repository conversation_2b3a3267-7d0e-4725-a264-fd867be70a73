import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';

import '../logging/app_logger.dart';

/// Render performance optimizer
///
/// This service provides rendering optimization capabilities:
/// - Widget caching and reuse
/// - Render performance monitoring
/// - Frame rate optimization
/// - Smooth scrolling optimization
class RenderOptimizer {
  static final RenderOptimizer _instance = RenderOptimizer._internal();
  factory RenderOptimizer() => _instance;
  RenderOptimizer._internal();

  Timer? _frameRateMonitor;
  final List<Duration> _frameTimes = [];
  final int _maxFrameTimesSamples = 60; // Track last 60 frames

  /// Initialize render optimizer
  void initialize() {
    appLogger.performance('RenderOptimizer: Initializing render optimization');

    // Setup frame rate monitoring
    _setupFrameRateMonitoring();

    // Configure render settings
    _configureRenderSettings();

    appLogger.performance('RenderOptimizer: Render optimization initialized');
  }

  /// Setup frame rate monitoring
  void _setupFrameRateMonitoring() {
    if (kDebugMode) {
      WidgetsBinding.instance.addPersistentFrameCallback((timeStamp) {
        _recordFrameTime(timeStamp);
      });
    }
  }

  /// Configure render settings for optimal performance
  void _configureRenderSettings() {
    // Enable render caching
    debugProfileBuildsEnabled = false;
    debugProfilePaintsEnabled = false;

    // Optimize for performance
    if (!kDebugMode) {
      debugPaintSizeEnabled = false;
    }
  }

  /// Record frame time for monitoring
  void _recordFrameTime(Duration timeStamp) {
    _frameTimes.add(timeStamp);

    // Keep only recent samples
    if (_frameTimes.length > _maxFrameTimesSamples) {
      _frameTimes.removeAt(0);
    }
  }

  /// Build optimized scrollable widget
  static Widget buildOptimizedScrollView({
    required List<Widget> children,
    ScrollController? controller,
    Axis scrollDirection = Axis.vertical,
    bool reverse = false,
    ScrollPhysics? physics,
    bool shrinkWrap = false,
    EdgeInsetsGeometry? padding,
  }) {
    return ListView(
      controller: controller,
      scrollDirection: scrollDirection,
      reverse: reverse,
      physics: physics ?? const BouncingScrollPhysics(),
      shrinkWrap: shrinkWrap,
      padding: padding,
      cacheExtent: 200.0, // Optimize cache extent
      children: children.map((child) => RepaintBoundary(child: child)).toList(),
    );
  }

  /// Build optimized list view
  static Widget buildOptimizedListView<T>({
    required List<T> items,
    required Widget Function(BuildContext context, T item, int index) itemBuilder,
    ScrollController? controller,
    Axis scrollDirection = Axis.vertical,
    bool reverse = false,
    ScrollPhysics? physics,
    bool shrinkWrap = false,
    EdgeInsetsGeometry? padding,
    double? cacheExtent,
    bool addAutomaticKeepAlives = false,
    bool addRepaintBoundaries = true,
  }) {
    return ListView.builder(
      controller: controller,
      scrollDirection: scrollDirection,
      reverse: reverse,
      physics: physics ?? const BouncingScrollPhysics(),
      shrinkWrap: shrinkWrap,
      padding: padding,
      cacheExtent: cacheExtent ?? 200.0,
      addAutomaticKeepAlives: addAutomaticKeepAlives,
      addRepaintBoundaries: addRepaintBoundaries,
      itemCount: items.length,
      itemBuilder: (context, index) {
        if (index >= items.length) return const SizedBox.shrink();

        Widget child = itemBuilder(context, items[index], index);

        // Wrap in RepaintBoundary for better performance
        if (addRepaintBoundaries) {
          child = RepaintBoundary(child: child);
        }

        return child;
      },
    );
  }

  /// Build optimized grid view
  static Widget buildOptimizedGridView<T>({
    required List<T> items,
    required Widget Function(BuildContext context, T item, int index) itemBuilder,
    required SliverGridDelegate gridDelegate,
    ScrollController? controller,
    Axis scrollDirection = Axis.vertical,
    bool reverse = false,
    ScrollPhysics? physics,
    bool shrinkWrap = false,
    EdgeInsetsGeometry? padding,
    double? cacheExtent,
    bool addAutomaticKeepAlives = false,
    bool addRepaintBoundaries = true,
  }) {
    return GridView.builder(
      controller: controller,
      scrollDirection: scrollDirection,
      reverse: reverse,
      physics: physics ?? const BouncingScrollPhysics(),
      shrinkWrap: shrinkWrap,
      padding: padding,
      cacheExtent: cacheExtent ?? 200.0,
      addAutomaticKeepAlives: addAutomaticKeepAlives,
      addRepaintBoundaries: addRepaintBoundaries,
      gridDelegate: gridDelegate,
      itemCount: items.length,
      itemBuilder: (context, index) {
        if (index >= items.length) return const SizedBox.shrink();

        Widget child = itemBuilder(context, items[index], index);

        // Wrap in RepaintBoundary for better performance
        if (addRepaintBoundaries) {
          child = RepaintBoundary(child: child);
        }

        return child;
      },
    );
  }

  /// Create optimized animated widget
  static Widget buildOptimizedAnimatedWidget({
    required Widget child,
    required Animation<double> animation,
    bool addRepaintBoundary = true,
  }) {
    Widget animatedChild = AnimatedBuilder(
      animation: animation,
      builder: (context, _) => child,
    );

    if (addRepaintBoundary) {
      animatedChild = RepaintBoundary(child: animatedChild);
    }

    return animatedChild;
  }

  /// Create performance-optimized card widget
  static Widget buildOptimizedCard({
    required Widget child,
    EdgeInsetsGeometry? margin,
    EdgeInsetsGeometry? padding,
    Color? color,
    double? elevation,
    ShapeBorder? shape,
    Clip clipBehavior = Clip.none,
    bool addRepaintBoundary = true,
  }) {
    Widget cardChild = Card(
      margin: margin,
      color: color,
      elevation: elevation,
      shape: shape,
      clipBehavior: clipBehavior,
      child: padding != null ? Padding(padding: padding, child: child) : child,
    );

    if (addRepaintBoundary) {
      cardChild = RepaintBoundary(child: cardChild);
    }

    return cardChild;
  }

  /// Create optimized image widget with caching
  static Widget buildOptimizedCachedImage({
    required String imageUrl,
    double? width,
    double? height,
    BoxFit? fit,
    Widget? placeholder,
    Widget? errorWidget,
    bool addRepaintBoundary = true,
  }) {
    Widget imageWidget = Image.network(
      imageUrl,
      width: width,
      height: height,
      fit: fit,
      cacheWidth: width?.toInt(),
      cacheHeight: height?.toInt(),
      filterQuality: FilterQuality.medium,
      loadingBuilder: (context, child, loadingProgress) {
        if (loadingProgress == null) return child;
        return placeholder ??
          SizedBox(
            width: width,
            height: height,
            child: const Center(child: CircularProgressIndicator()),
          );
      },
      errorBuilder: (context, error, stackTrace) {
        return errorWidget ??
          SizedBox(
            width: width,
            height: height,
            child: const Icon(Icons.error),
          );
      },
    );

    if (addRepaintBoundary) {
      imageWidget = RepaintBoundary(child: imageWidget);
    }

    return imageWidget;
  }

  /// Get current frame rate
  double getCurrentFrameRate() {
    if (_frameTimes.length < 2) return 0.0;

    final totalTime = _frameTimes.last - _frameTimes.first;
    final frameCount = _frameTimes.length - 1;

    if (totalTime.inMicroseconds == 0) return 0.0;

    return (frameCount * 1000000) / totalTime.inMicroseconds;
  }

  /// Get average frame time
  Duration getAverageFrameTime() {
    if (_frameTimes.length < 2) return Duration.zero;

    int totalMicroseconds = 0;
    for (int i = 1; i < _frameTimes.length; i++) {
      totalMicroseconds += (_frameTimes[i] - _frameTimes[i - 1]).inMicroseconds;
    }

    return Duration(microseconds: totalMicroseconds ~/ (_frameTimes.length - 1));
  }

  /// Get render optimization report
  RenderOptimizationReport getReport() {
    return RenderOptimizationReport(
      currentFrameRate: getCurrentFrameRate(),
      averageFrameTime: getAverageFrameTime(),
      frameTimesSamples: _frameTimes.length,
      maxFrameTimesSamples: _maxFrameTimesSamples,
    );
  }

  /// Dispose resources
  void dispose() {
    _frameRateMonitor?.cancel();
    _frameTimes.clear();
  }
}

/// Render optimization report
class RenderOptimizationReport {
  final double currentFrameRate;
  final Duration averageFrameTime;
  final int frameTimesSamples;
  final int maxFrameTimesSamples;

  RenderOptimizationReport({
    required this.currentFrameRate,
    required this.averageFrameTime,
    required this.frameTimesSamples,
    required this.maxFrameTimesSamples,
  });

  bool get isPerformanceGood => currentFrameRate >= 55.0; // Close to 60 FPS
  double get averageFrameTimeMs => averageFrameTime.inMicroseconds / 1000.0;

  @override
  String toString() {
    return '''RenderOptimizationReport(
  Current Frame Rate: ${currentFrameRate.toStringAsFixed(1)} FPS
  Average Frame Time: ${averageFrameTimeMs.toStringAsFixed(2)}ms
  Frame Samples: $frameTimesSamples/$maxFrameTimesSamples
  Performance: ${isPerformanceGood ? 'Good' : 'Needs Improvement'}
)''';
  }
}
