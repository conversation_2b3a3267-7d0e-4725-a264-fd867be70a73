import 'dart:async';
import 'dart:convert';

import 'package:dio/dio.dart';

import '../logging/app_logger.dart';

/// Network performance optimizer
/// 
/// This service provides network optimization capabilities:
/// - Request caching and deduplication
/// - Intelligent retry mechanisms
/// - Network performance monitoring
class NetworkOptimizer {
  static final NetworkOptimizer _instance = NetworkOptimizer._internal();
  factory NetworkOptimizer() => _instance;
  NetworkOptimizer._internal();

  // Request cache
  final Map<String, CachedResponse> _responseCache = {};
  
  // Pending requests (for deduplication)
  final Map<String, Future<Response>> _pendingRequests = {};

  /// Initialize network optimizer
  void initialize() {
    appLogger.performance('NetworkOptimizer: Initializing network optimization');
    
    // Start cache cleanup timer
    _startCacheCleanup();
    
    appLogger.performance('NetworkOptimizer: Network optimization initialized');
  }

  /// Create optimized Dio instance
  Dio createOptimizedDio({
    String? baseUrl,
    Duration? connectTimeout,
    Duration? receiveTimeout,
    Duration? sendTimeout,
  }) {
    final dio = Dio(BaseOptions(
      baseUrl: baseUrl ?? '',
      connectTimeout: connectTimeout ?? const Duration(seconds: 10),
      receiveTimeout: receiveTimeout ?? const Duration(seconds: 30),
      sendTimeout: sendTimeout ?? const Duration(seconds: 30),
      headers: {
        'Accept-Encoding': 'gzip, deflate',
        'Connection': 'keep-alive',
      },
    ));

    // Add performance interceptor
    dio.interceptors.add(_createPerformanceInterceptor());
    
    // Add caching interceptor
    dio.interceptors.add(_createCachingInterceptor());

    return dio;
  }

  /// Perform optimized request with caching and deduplication
  Future<Response<T>> optimizedRequest<T>(
    String url, {
    String method = 'GET',
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    Duration? cacheDuration,
    bool enableDeduplication = true,
  }) async {
    final requestKey = _generateRequestKey(url, method, queryParameters, data);
    
    // Check cache first
    if (method == 'GET' && cacheDuration != null) {
      final cachedResponse = _getCachedResponse(requestKey);
      if (cachedResponse != null) {
        appLogger.debug('NetworkOptimizer: Cache hit for $url');
        return cachedResponse as Response<T>;
      }
    }

    // Check for pending request (deduplication)
    if (enableDeduplication && _pendingRequests.containsKey(requestKey)) {
      appLogger.debug('NetworkOptimizer: Deduplicating request for $url');
      return await _pendingRequests[requestKey]! as Response<T>;
    }

    // Create new request
    final dio = createOptimizedDio();
    
    final future = dio.request<T>(
      url,
      data: data,
      queryParameters: queryParameters,
      options: (options ?? Options()).copyWith(method: method),
    );

    // Store pending request for deduplication
    if (enableDeduplication) {
      _pendingRequests[requestKey] = future;
    }

    try {
      final response = await future;
      
      // Cache successful GET requests
      if (method == 'GET' && cacheDuration != null && response.statusCode == 200) {
        _cacheResponse(requestKey, response, cacheDuration);
      }
      
      return response;
    } finally {
      // Remove from pending requests
      _pendingRequests.remove(requestKey);
    }
  }

  /// Create performance monitoring interceptor
  Interceptor _createPerformanceInterceptor() {
    return InterceptorsWrapper(
      onRequest: (options, handler) {
        final startTime = DateTime.now();
        options.extra['startTime'] = startTime;
        handler.next(options);
      },
      onResponse: (response, handler) {
        final startTime = response.requestOptions.extra['startTime'] as DateTime?;
        if (startTime != null) {
          final duration = DateTime.now().difference(startTime);
          appLogger.debug('NetworkOptimizer: ${response.requestOptions.method} ${response.requestOptions.uri} completed in ${duration.inMilliseconds}ms');
        }
        handler.next(response);
      },
      onError: (error, handler) {
        final startTime = error.requestOptions.extra['startTime'] as DateTime?;
        if (startTime != null) {
          final duration = DateTime.now().difference(startTime);
          appLogger.warning('NetworkOptimizer: ${error.requestOptions.method} ${error.requestOptions.uri} failed after ${duration.inMilliseconds}ms');
        }
        handler.next(error);
      },
    );
  }

  /// Create caching interceptor
  Interceptor _createCachingInterceptor() {
    return InterceptorsWrapper(
      onRequest: (options, handler) {
        // Caching is handled in optimizedRequest method
        handler.next(options);
      },
    );
  }

  /// Generate unique key for request
  String _generateRequestKey(String url, String method, Map<String, dynamic>? queryParameters, dynamic data) {
    final buffer = StringBuffer();
    buffer.write('$method:$url');
    
    if (queryParameters != null && queryParameters.isNotEmpty) {
      final sortedParams = Map.fromEntries(
        queryParameters.entries.toList()..sort((a, b) => a.key.compareTo(b.key))
      );
      buffer.write('?${Uri(queryParameters: sortedParams).query}');
    }
    
    if (data != null) {
      buffer.write(':${jsonEncode(data)}');
    }
    
    return buffer.toString();
  }

  /// Get cached response
  Response? _getCachedResponse(String key) {
    final cached = _responseCache[key];
    if (cached != null && !cached.isExpired) {
      return cached.response;
    }
    
    // Remove expired cache entry
    if (cached != null) {
      _responseCache.remove(key);
    }
    
    return null;
  }

  /// Cache response
  void _cacheResponse(String key, Response response, Duration duration) {
    _responseCache[key] = CachedResponse(
      response: response,
      expiresAt: DateTime.now().add(duration),
    );
  }

  /// Start cache cleanup timer
  void _startCacheCleanup() {
    Timer.periodic(const Duration(minutes: 5), (_) {
      _cleanupExpiredCache();
    });
  }

  /// Clean up expired cache entries
  void _cleanupExpiredCache() {
    final expiredKeys = <String>[];
    
    for (final entry in _responseCache.entries) {
      if (entry.value.isExpired) {
        expiredKeys.add(entry.key);
      }
    }
    
    for (final key in expiredKeys) {
      _responseCache.remove(key);
    }
    
    if (expiredKeys.isNotEmpty) {
      appLogger.debug('NetworkOptimizer: Cleaned up ${expiredKeys.length} expired cache entries');
    }
  }

  /// Get network optimization report
  NetworkOptimizationReport getReport() {
    return NetworkOptimizationReport(
      cacheSize: _responseCache.length,
      pendingRequestsCount: _pendingRequests.length,
    );
  }

  /// Clear all caches
  void clearCache() {
    _responseCache.clear();
    _pendingRequests.clear();
    appLogger.debug('NetworkOptimizer: All caches cleared');
  }

  /// Dispose resources
  void dispose() {
    _responseCache.clear();
    _pendingRequests.clear();
  }
}

/// Cached response wrapper
class CachedResponse {
  final Response response;
  final DateTime expiresAt;

  CachedResponse({
    required this.response,
    required this.expiresAt,
  });

  bool get isExpired => DateTime.now().isAfter(expiresAt);
}

/// Network optimization report
class NetworkOptimizationReport {
  final int cacheSize;
  final int pendingRequestsCount;

  NetworkOptimizationReport({
    required this.cacheSize,
    required this.pendingRequestsCount,
  });

  @override
  String toString() {
    return '''NetworkOptimizationReport(
  Cache Size: $cacheSize
  Pending Requests: $pendingRequestsCount
)''';
  }
}
