import 'dart:io';
import 'package:dio/dio.dart';
import 'package:dio_cache_interceptor/dio_cache_interceptor.dart';
import 'package:http_cache_hive_store/http_cache_hive_store.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:path_provider/path_provider.dart';
import 'package:flutter/foundation.dart';
import '../logging/app_logger.dart';

/// 智能缓存拦截器
///
/// 基于 http_cache_hive_store 的企业级智能缓存系统
class SmartCacheInterceptor extends Interceptor {
  late final DioCacheInterceptor _cacheInterceptor;
  final Connectivity _connectivity = Connectivity();

  SmartCacheInterceptor._();

  static Future<SmartCacheInterceptor> create() async {
    final interceptor = SmartCacheInterceptor._();
    await interceptor._initialize();
    return interceptor;
  }

  Future<void> _initialize() async {
    late final Directory cacheDir;

    if (kIsWeb) {
      // Web 平台使用内存缓存
      if (kDebugMode) {
        appLogger.network('Using memory cache for Web platform');
      }

      final cacheOptions = CacheOptions(
        store: MemCacheStore(),
        policy: CachePolicy.request,
        maxStale: const Duration(hours: 1), // Web 平台缓存时间较短
        priority: CachePriority.normal,
        allowPostMethod: false,
        hitCacheOnNetworkFailure: true,
        hitCacheOnErrorCodes: [500, 502, 503, 504],
      );

      _cacheInterceptor = DioCacheInterceptor(options: cacheOptions);
    } else {
      // 其他平台使用文件缓存
      final appDocDir = await getApplicationDocumentsDirectory();
      cacheDir = Directory('${appDocDir.path}/dio_cache');

      if (!await cacheDir.exists()) {
        await cacheDir.create(recursive: true);
      }

      if (kDebugMode) {
        appLogger.network('Using file cache for native platform: ${cacheDir.path}');
      }

      final cacheOptions = CacheOptions(
        store: HiveCacheStore(cacheDir.path),
        policy: CachePolicy.request,
        maxStale: const Duration(days: 7),
        priority: CachePriority.normal,
        allowPostMethod: false,
        hitCacheOnNetworkFailure: true,
        hitCacheOnErrorCodes: [500, 502, 503, 504],
      );

      _cacheInterceptor = DioCacheInterceptor(options: cacheOptions);
    }
  }

  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) async {
    // 应用智能缓存策略
    _applyCacheStrategy(options);

    // 网络状态感知缓存
    final connectivityResult = await _connectivity.checkConnectivity();
    final isOffline = connectivityResult.contains(ConnectivityResult.none);

    if (isOffline && _shouldCache(options)) {
      // 离线时强制使用缓存，允许过期数据
      options.extra.addAll(CacheOptions(
        store: HiveCacheStore(''),  // 临时存储，实际会被覆盖
        policy: CachePolicy.request,
        maxStale: const Duration(days: 30),
      ).toExtra());
    }

    _cacheInterceptor.onRequest(options, handler);
  }

  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) {
    _cacheInterceptor.onResponse(response, handler);
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) async {
    if (_isNetworkError(err) && _shouldCache(err.requestOptions)) {
      // 尝试从缓存恢复
      final cachedResponse = await _trySmartCacheRecovery(err.requestOptions);
      if (cachedResponse != null) {
        handler.resolve(cachedResponse);
        return;
      }
    }
    _cacheInterceptor.onError(err, handler);
  }

  /// 应用智能缓存策略
  void _applyCacheStrategy(RequestOptions options) {
    // 根据API类型设置不同的缓存策略
    if (options.path.contains('/api/user/profile') || options.path.contains('/api/user/info')) {
      // 用户信息：中等缓存时间
      options.extra.addAll(CacheOptions(
        store: HiveCacheStore(''),
        policy: CachePolicy.request,
        maxStale: const Duration(minutes: 15), // 减少到15分钟，保持数据新鲜度
        priority: CachePriority.high, // 用户信息高优先级
      ).toExtra());
    } else if (options.path.contains('/api/config/') || options.path.contains('/api/settings/')) {
      // 配置信息：长期缓存
      options.extra.addAll(CacheOptions(
        store: HiveCacheStore(''),
        policy: CachePolicy.request,
        maxStale: const Duration(hours: 24),
        priority: CachePriority.normal,
      ).toExtra());
    } else if (options.path.contains('/api/products') || options.path.contains('/api/catalog')) {
      // 产品信息：中期缓存
      options.extra.addAll(CacheOptions(
        store: HiveCacheStore(''),
        policy: CachePolicy.request,
        maxStale: const Duration(hours: 2),
        priority: CachePriority.normal,
      ).toExtra());
    } else if (options.path.contains('/api/news') || options.path.contains('/api/articles')) {
      // 新闻文章：短期缓存
      options.extra.addAll(CacheOptions(
        store: HiveCacheStore(''),
        policy: CachePolicy.request,
        maxStale: const Duration(minutes: 30),
        priority: CachePriority.low,
      ).toExtra());
    }
  }

  /// 智能判断是否应该缓存
  bool _shouldCache(RequestOptions options) {
    // 只缓存GET请求
    if (options.method.toLowerCase() != 'get') return false;

    // 不缓存的路径
    final noCachePaths = [
      '/auth/',           // 认证相关
      '/upload/',         // 文件上传
      '/download/',       // 文件下载
      '/realtime/',       // 实时数据
      '/websocket/',      // WebSocket
      '/stream/',         // 流数据
      '/payment/',        // 支付相关
      '/otp/',           // 一次性密码
      '/captcha/',       // 验证码
    ];

    for (final path in noCachePaths) {
      if (options.path.contains(path)) return false;
    }

    // 检查查询参数中是否包含时间戳或随机数（通常表示不应缓存）
    final queryParams = options.queryParameters;
    if (queryParams.containsKey('timestamp') ||
        queryParams.containsKey('_t') ||
        queryParams.containsKey('random')) {
      return false;
    }

    return true;
  }

  /// 判断是否为网络错误
  bool _isNetworkError(DioException err) {
    return err.type == DioExceptionType.connectionTimeout ||
           err.type == DioExceptionType.receiveTimeout ||
           err.type == DioExceptionType.connectionError ||
           err.type == DioExceptionType.sendTimeout;
  }

  /// 智能缓存恢复
  Future<Response?> _trySmartCacheRecovery(RequestOptions options) async {
    try {
      if (kDebugMode) {
        appLogger.network('Attempting smart cache recovery for: ${options.path}');
      }

      // 这里可以实现更复杂的缓存恢复逻辑
      // 例如：
      // 1. 检查缓存是否存在过期数据
      // 2. 返回带有特殊标记的响应
      // 3. 实现多级缓存降级策略
      // 目前返回 null，让 DioCacheInterceptor 处理标准缓存逻辑
      return null;
    } catch (e) {
      return null;
    }
  }
}

/// 缓存策略配置类
class CacheStrategy {
  final Duration duration;
  final CachePolicy policy;
  final bool staleWhileRevalidate;
  final int? maxSize;
  final List<int>? allowedStatusCodes;

  const CacheStrategy({
    required this.duration,
    required this.policy,
    this.staleWhileRevalidate = false,
    this.maxSize,
    this.allowedStatusCodes,
  });
}
