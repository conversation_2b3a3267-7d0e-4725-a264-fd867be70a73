import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import '../logging/app_logger.dart';

/// 性能监控拦截器
///
/// 监控网络请求的性能指标，包括：
/// - 请求响应时间
/// - 请求大小和响应大小
/// - 慢请求检测和警告
/// - 性能统计和分析
class PerformanceInterceptor extends Interceptor {
  static const Duration _slowRequestThreshold = Duration(seconds: 3);
  static const int _largeSizeThreshold = 1024 * 1024; // 1MB

  final Map<String, DateTime> _requestStartTimes = {};
  final List<RequestMetrics> _metrics = [];

  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    final requestId = _generateRequestId(options);
    _requestStartTimes[requestId] = DateTime.now();

    if (kDebugMode) {
      final requestSize = _calculateRequestSize(options);
      if (requestSize > _largeSizeThreshold) {
        appLogger.warning('Large request detected: ${options.path} (${_formatBytes(requestSize)})');
      }
    }

    handler.next(options);
  }

  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) {
    final requestId = _generateRequestId(response.requestOptions);
    final startTime = _requestStartTimes.remove(requestId);

    if (startTime != null) {
      final duration = DateTime.now().difference(startTime);
      final responseSize = _calculateResponseSize(response);

      // 记录性能指标
      final metrics = RequestMetrics(
        path: response.requestOptions.path,
        method: response.requestOptions.method,
        duration: duration,
        statusCode: response.statusCode ?? 0,
        requestSize: _calculateRequestSize(response.requestOptions),
        responseSize: responseSize,
        timestamp: startTime,
      );

      _addMetrics(metrics);

      // 检测慢请求
      if (duration > _slowRequestThreshold) {
        appLogger.warning(
          'Slow request detected: ${response.requestOptions.method} ${response.requestOptions.path} '
          '(${duration.inMilliseconds}ms)'
        );
      }

      // 检测大响应
      if (responseSize > _largeSizeThreshold) {
        appLogger.warning(
          'Large response detected: ${response.requestOptions.path} '
          '(${_formatBytes(responseSize)})'
        );
      }

      if (kDebugMode) {
        appLogger.debug(
          'Request completed: ${response.requestOptions.method} ${response.requestOptions.path} '
          '- ${duration.inMilliseconds}ms - ${response.statusCode}'
        );
      }
    }

    handler.next(response);
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    final requestId = _generateRequestId(err.requestOptions);
    final startTime = _requestStartTimes.remove(requestId);

    if (startTime != null) {
      final duration = DateTime.now().difference(startTime);

      // 记录错误指标
      final metrics = RequestMetrics(
        path: err.requestOptions.path,
        method: err.requestOptions.method,
        duration: duration,
        statusCode: err.response?.statusCode ?? 0,
        requestSize: _calculateRequestSize(err.requestOptions),
        responseSize: err.response != null ? _calculateResponseSize(err.response!) : 0,
        timestamp: startTime,
        hasError: true,
        errorType: err.type.toString(),
      );

      _addMetrics(metrics);

      appLogger.error(
        'Request failed: ${err.requestOptions.method} ${err.requestOptions.path} '
        '- ${duration.inMilliseconds}ms - ${err.type}',
        err
      );
    }

    handler.next(err);
  }

  /// 生成请求ID
  String _generateRequestId(RequestOptions options) {
    return '${options.method}_${options.path}_${options.hashCode}';
  }

  /// 计算请求大小
  int _calculateRequestSize(RequestOptions options) {
    int size = 0;

    // Headers
    options.headers.forEach((key, value) {
      size += key.length + value.toString().length;
    });

    // Query parameters
    options.queryParameters.forEach((key, value) {
      size += key.length + value.toString().length;
    });

    // Body
    if (options.data != null) {
      if (options.data is String) {
        size += (options.data as String).length;
      } else if (options.data is List<int>) {
        size += (options.data as List<int>).length;
      } else {
        // 估算其他类型的大小
        size += options.data.toString().length;
      }
    }

    return size;
  }

  /// 计算响应大小
  int _calculateResponseSize(Response response) {
    if (response.data == null) return 0;

    if (response.data is String) {
      return (response.data as String).length;
    } else if (response.data is List<int>) {
      return (response.data as List<int>).length;
    } else {
      // 估算其他类型的大小
      return response.data.toString().length;
    }
  }

  /// 添加性能指标
  void _addMetrics(RequestMetrics metrics) {
    _metrics.add(metrics);

    // 保持最近1000条记录
    if (_metrics.length > 1000) {
      _metrics.removeAt(0);
    }
  }

  /// 格式化字节大小
  String _formatBytes(int bytes) {
    if (bytes < 1024) return '${bytes}B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)}KB';
    return '${(bytes / (1024 * 1024)).toStringAsFixed(1)}MB';
  }

  /// 获取性能统计
  PerformanceStats getStats() {
    if (_metrics.isEmpty) {
      return PerformanceStats.empty();
    }

    final durations = _metrics.map((m) => m.duration.inMilliseconds).toList();
    final requestSizes = _metrics.map((m) => m.requestSize).toList();
    final responseSizes = _metrics.map((m) => m.responseSize).toList();

    durations.sort();
    requestSizes.sort();
    responseSizes.sort();

    return PerformanceStats(
      totalRequests: _metrics.length,
      errorCount: _metrics.where((m) => m.hasError).length,
      averageResponseTime: durations.reduce((a, b) => a + b) / durations.length,
      p50ResponseTime: durations[durations.length ~/ 2].toDouble(),
      p95ResponseTime: durations[(durations.length * 0.95).floor()].toDouble(),
      p99ResponseTime: durations[(durations.length * 0.99).floor()].toDouble(),
      averageRequestSize: requestSizes.reduce((a, b) => a + b) / requestSizes.length,
      averageResponseSize: responseSizes.reduce((a, b) => a + b) / responseSizes.length,
      slowRequestCount: _metrics.where((m) => m.duration > _slowRequestThreshold).length,
    );
  }

  /// 清除指标
  void clearMetrics() {
    _metrics.clear();
    _requestStartTimes.clear();
  }
}

/// 请求性能指标
class RequestMetrics {
  final String path;
  final String method;
  final Duration duration;
  final int statusCode;
  final int requestSize;
  final int responseSize;
  final DateTime timestamp;
  final bool hasError;
  final String? errorType;

  const RequestMetrics({
    required this.path,
    required this.method,
    required this.duration,
    required this.statusCode,
    required this.requestSize,
    required this.responseSize,
    required this.timestamp,
    this.hasError = false,
    this.errorType,
  });
}

/// 性能统计
class PerformanceStats {
  final int totalRequests;
  final int errorCount;
  final double averageResponseTime;
  final double p50ResponseTime;
  final double p95ResponseTime;
  final double p99ResponseTime;
  final double averageRequestSize;
  final double averageResponseSize;
  final int slowRequestCount;

  const PerformanceStats({
    required this.totalRequests,
    required this.errorCount,
    required this.averageResponseTime,
    required this.p50ResponseTime,
    required this.p95ResponseTime,
    required this.p99ResponseTime,
    required this.averageRequestSize,
    required this.averageResponseSize,
    required this.slowRequestCount,
  });

  factory PerformanceStats.empty() {
    return const PerformanceStats(
      totalRequests: 0,
      errorCount: 0,
      averageResponseTime: 0,
      p50ResponseTime: 0,
      p95ResponseTime: 0,
      p99ResponseTime: 0,
      averageRequestSize: 0,
      averageResponseSize: 0,
      slowRequestCount: 0,
    );
  }

  double get errorRate => totalRequests > 0 ? errorCount / totalRequests : 0;
  double get slowRequestRate => totalRequests > 0 ? slowRequestCount / totalRequests : 0;
}
