import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import '../auth/token_manager.dart';

class TokenInterceptor extends Interceptor {
  final TokenManager _tokenManager;
  final Dio _dio;

  TokenInterceptor(this._tokenManager, this._dio);

  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) async {
    final token = await _tokenManager.getAccessToken();
    if (token != null && token.isNotEmpty) {
      options.headers['Authorization'] = 'Bearer $token';
    }
    handler.next(options);
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) async {
    if (err.response?.statusCode == 401) {
      // Token expired, try to refresh
      final refreshed = await _refreshToken();
      if (refreshed) {
        // Retry the original request
        final clonedRequest = await _retryRequest(err.requestOptions);
        handler.resolve(clonedRequest);
        return;
      } else {
        // Refresh failed, redirect to login
        await _clearTokens();
      }
    }
    handler.next(err);
  }

  Future<bool> _refreshToken() async {
    try {
      final refreshToken = await _tokenManager.getRefreshToken();
      if (refreshToken == null) return false;

      final response = await _dio.post(
        '/auth/refresh',
        data: {'refresh_token': refreshToken},
        options: Options(
          headers: {'Authorization': null}, // Don't include old token
        ),
      );

      if (response.statusCode == 200) {
        final newAccessToken = response.data['access_token'];
        final newRefreshToken = response.data['refresh_token'];
        final tokenExpiry = response.data['token_expiry'];

        await _tokenManager.updateTokens(
          accessToken: newAccessToken,
          refreshToken: newRefreshToken,
          tokenExpiry: tokenExpiry,
        );

        return true;
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Token refresh failed: $e');
      }
    }
    return false;
  }

  Future<Response<dynamic>> _retryRequest(RequestOptions requestOptions) async {
    final token = await _tokenManager.getAccessToken();
    if (token != null) {
      requestOptions.headers['Authorization'] = 'Bearer $token';
    }

    return await _dio.request(
      requestOptions.path,
      data: requestOptions.data,
      queryParameters: requestOptions.queryParameters,
      options: Options(
        method: requestOptions.method,
        headers: requestOptions.headers,
      ),
    );
  }

  Future<void> _clearTokens() async {
    await _tokenManager.clearAuthData();
  }
}