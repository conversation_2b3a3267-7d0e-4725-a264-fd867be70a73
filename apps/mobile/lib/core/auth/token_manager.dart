import 'package:injectable/injectable.dart';
import 'package:flutter/foundation.dart';
import '../storage/secure_storage_service.dart';
import '../logging/app_logger.dart';

/// Token管理器
///
/// 负责管理用户认证令牌的存储、获取、验证和更新
/// 提供统一的令牌操作接口，支持访问令牌、刷新令牌的生命周期管理
@lazySingleton
class TokenManager {
  final SecureStorageService _secureStorage;

  TokenManager(this._secureStorage);

  /// 保存完整的认证数据
  Future<void> saveAuthData({
    required String accessToken,
    required String refreshToken,
    required String tokenExpiry,
    required String userId,
  }) async {
    try {
      if (kDebugMode) {
        appLogger.auth('Saving auth data', userId: userId);
      }

      await _secureStorage.storeAuthData(
        accessToken: accessToken,
        refreshToken: refreshToken,
        tokenExpiry: tokenExpiry,
        userId: userId,
      );

      if (kDebugMode) {
        appLogger.auth('Auth data saved successfully', userId: userId);
      }
    } catch (e) {
      if (kDebugMode) {
        appLogger.auth('Failed to save auth data', success: false, error: e);
      }
      rethrow;
    }
  }

  /// 获取访问令牌
  Future<String?> getAccessToken() async {
    try {
      final token = await _secureStorage.getAccessToken();
      if (kDebugMode) {
        if (token != null) {
          print('TokenManager: Access token retrieved');
        } else {
          print('TokenManager: No access token found');
        }
      }
      return token;
    } catch (e) {
      if (kDebugMode) {
        print('TokenManager: Failed to get access token: $e');
      }
      return null;
    }
  }

  /// 获取刷新令牌
  Future<String?> getRefreshToken() async {
    try {
      final token = await _secureStorage.getRefreshToken();
      if (kDebugMode) {
        print('TokenManager: Refresh token operation completed');
      }
      return token;
    } catch (e) {
      if (kDebugMode) {
        print('TokenManager: Failed to get refresh token: $e');
      }
      return null;
    }
  }

  /// 获取用户ID
  Future<String?> getUserId() async {
    try {
      return await _secureStorage.getUserId();
    } catch (e) {
      if (kDebugMode) {
        print('TokenManager: Failed to get user ID: $e');
      }
      return null;
    }
  }

  /// 获取令牌过期时间
  Future<String?> getTokenExpiry() async {
    try {
      return await _secureStorage.getTokenExpiry();
    } catch (e) {
      if (kDebugMode) {
        print('TokenManager: Failed to get token expiry: $e');
      }
      return null;
    }
  }

  /// 检查令牌是否过期
  Future<bool> isTokenExpired() async {
    try {
      final tokenExpiry = await _secureStorage.getTokenExpiry();
      if (tokenExpiry == null) {
        return true;
      }

      final expiryDate = DateTime.parse(tokenExpiry);
      final isExpired = DateTime.now().isAfter(expiryDate);

      if (kDebugMode) {
        print('TokenManager: Token expiry check completed - expired: $isExpired');
      }

      return isExpired;
    } catch (e) {
      if (kDebugMode) {
        print('TokenManager: Failed to check token expiry: $e');
      }
      return true;
    }
  }

  /// 验证是否有有效的认证数据
  Future<bool> hasValidAuthData() async {
    try {
      final hasData = await _secureStorage.hasValidAuthData();
      return hasData && !await isTokenExpired();
    } catch (e) {
      if (kDebugMode) {
        print('TokenManager: Failed to validate auth data: $e');
      }
      return false;
    }
  }

  /// 清除所有认证数据
  Future<void> clearAuthData() async {
    try {
      await _secureStorage.clearAuthData();
      if (kDebugMode) {
        print('TokenManager: Auth data cleared successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('TokenManager: Failed to clear auth data: $e');
      }
      rethrow;
    }
  }

  /// 更新访问令牌
  Future<void> updateAccessToken(String newToken) async {
    try {
      await _secureStorage.storeAccessToken(newToken);
      if (kDebugMode) {
        print('TokenManager: Access token updated successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('TokenManager: Failed to update access token: $e');
      }
      rethrow;
    }
  }

  /// 更新刷新令牌
  Future<void> updateRefreshToken(String newToken) async {
    try {
      await _secureStorage.storeRefreshToken(newToken);
      if (kDebugMode) {
        print('TokenManager: Refresh token updated successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('TokenManager: Failed to update refresh token: $e');
      }
      rethrow;
    }
  }

  /// 更新令牌过期时间
  Future<void> updateTokenExpiry(String newExpiry) async {
    try {
      await _secureStorage.storeTokenExpiry(newExpiry);
      if (kDebugMode) {
        print('TokenManager: Token expiry updated successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('TokenManager: Failed to update token expiry: $e');
      }
      rethrow;
    }
  }

  /// 批量更新令牌信息
  Future<void> updateTokens({
    String? accessToken,
    String? refreshToken,
    String? tokenExpiry,
  }) async {
    try {
      final futures = <Future<void>>[];

      if (accessToken != null) {
        futures.add(_secureStorage.storeAccessToken(accessToken));
      }

      if (refreshToken != null) {
        futures.add(_secureStorage.storeRefreshToken(refreshToken));
      }

      if (tokenExpiry != null) {
        futures.add(_secureStorage.storeTokenExpiry(tokenExpiry));
      }

      await Future.wait(futures);
      if (kDebugMode) {
        print('TokenManager: Tokens updated successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('TokenManager: Failed to update tokens: $e');
      }
      rethrow;
    }
  }

  /// 获取令牌剩余有效时间
  Future<Duration?> getTokenRemainingTime() async {
    try {
      final tokenExpiry = await _secureStorage.getTokenExpiry();
      if (tokenExpiry == null) {
        return Duration.zero;
      }

      final expiryDate = DateTime.parse(tokenExpiry);
      final remaining = expiryDate.difference(DateTime.now());

      return remaining.isNegative ? Duration.zero : remaining;
    } catch (e) {
      if (kDebugMode) {
        print('TokenManager: Failed to get token remaining time: $e');
      }
      return null;
    }
  }

  /// 检查令牌是否即将过期
  Future<bool> isTokenExpiringSoon({Duration threshold = const Duration(minutes: 5)}) async {
    try {
      final remaining = await getTokenRemainingTime();
      if (remaining == null) return true;

      final expiringSoon = remaining <= threshold;
      if (kDebugMode) {
        print('TokenManager: Token expiring soon check completed - expiring: $expiringSoon');
      }

      return expiringSoon;
    } catch (e) {
      if (kDebugMode) {
        print('TokenManager: Failed to check if token expiring soon: $e');
      }
      return true;
    }
  }

  /// 生成新的令牌过期时间
  String generateTokenExpiry({Duration validDuration = const Duration(hours: 24)}) {
    return DateTime.now().add(validDuration).toIso8601String();
  }
}
