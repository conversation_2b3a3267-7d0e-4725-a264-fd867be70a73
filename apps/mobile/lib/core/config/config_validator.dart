import 'package:flutter/foundation.dart';
import 'environment_config.dart';
import '../logging/app_logger.dart';

/// 环境配置验证器
///
/// 用于验证环境配置是否正确加载和设置
class ConfigValidator {
  /// 验证所有必需的环境配置
  static bool validateConfig() {
    final issues = <String>[];

    // 验证 API 配置
    if (EnvironmentConfig.apiBaseUrl.isEmpty) {
      issues.add('API_BASE_URL is not configured');
    }

    if (!EnvironmentConfig.apiBaseUrl.startsWith('http')) {
      issues.add('API_BASE_URL must start with http:// or https://');
    }

    // 验证网络超时配置
    if (EnvironmentConfig.connectTimeout.inSeconds <= 0) {
      issues.add('CONNECT_TIMEOUT must be greater than 0');
    }

    if (EnvironmentConfig.receiveTimeout.inSeconds <= 0) {
      issues.add('RECEIVE_TIMEOUT must be greater than 0');
    }

    // 验证安全配置（生产环境必需）
    if (EnvironmentConfig.isProduction) {
      if (EnvironmentConfig.encryptionKey.isEmpty ||
          EnvironmentConfig.encryptionKey.contains('REPLACE_WITH')) {
        issues.add('ENCRYPTION_KEY must be configured for production');
      }

      if (EnvironmentConfig.appSecretKey.isEmpty ||
          EnvironmentConfig.appSecretKey.contains('REPLACE_WITH')) {
        issues.add('APP_SECRET_KEY must be configured for production');
      }
    }

    // 验证缓存配置
    if (EnvironmentConfig.cacheExpiryMinutes <= 0) {
      issues.add('CACHE_EXPIRY_MINUTES must be greater than 0');
    }

    if (EnvironmentConfig.maxCacheSizeMB <= 0) {
      issues.add('MAX_CACHE_SIZE_MB must be greater than 0');
    }

    // 输出验证结果
    if (issues.isNotEmpty) {
      if (kDebugMode) {
        print('❌ Environment Configuration Issues:');
        for (final issue in issues) {
          print('  - $issue');
        }
      }
      return false;
    }

    if (kDebugMode) {
      print('✅ Environment configuration validation passed');
    }
    return true;
  }

  /// 打印配置摘要（仅在非生产环境）
  static void printConfigSummary() {
    if (EnvironmentConfig.isProduction) return;

    appLogger.info('\n📋 Configuration Summary:');
    appLogger.info('Environment: ${EnvironmentConfig.currentEnvironment}');
    appLogger.info('API URL: ${EnvironmentConfig.apiBaseUrl}');
    appLogger.info('WebSocket: ${EnvironmentConfig.websocketUrl}');
    appLogger.info('Timeouts: ${EnvironmentConfig.connectTimeout.inSeconds}s/${EnvironmentConfig.receiveTimeout.inSeconds}s/${EnvironmentConfig.sendTimeout.inSeconds}s');
    appLogger.info('Features: Logging=${EnvironmentConfig.enableLogging}, Analytics=${EnvironmentConfig.enableAnalytics}');
    appLogger.info('Database: ${EnvironmentConfig.databaseName}');
    appLogger.info('Cache: ${EnvironmentConfig.cacheExpiryMinutes}min, ${EnvironmentConfig.maxCacheSizeMB}MB');

    if (EnvironmentConfig.enableMockData) {
      appLogger.info('🧪 Mock data enabled with ${EnvironmentConfig.mockDelayMs}ms delay');
    }
    appLogger.info('');
  }

  /// 检查是否为有效的环境配置
  static bool isValidEnvironment(String environment) {
    return ['development', 'dev', 'staging', 'stag', 'production', 'prod']
        .contains(environment.toLowerCase());
  }

  /// 获取推荐的环境配置
  static Map<String, dynamic> getRecommendedConfig(String environment) {
    switch (environment.toLowerCase()) {
      case 'development':
      case 'dev':
        return {
          'ENABLE_LOGGING': 'true',
          'ENABLE_DEBUG_TOOLS': 'true',
          'ENABLE_ANALYTICS': 'false',
          'CONNECT_TIMEOUT': '30',
          'CACHE_EXPIRY_MINUTES': '5',
          'ENABLE_MOCK_DATA': 'true',
        };

      case 'staging':
      case 'stag':
        return {
          'ENABLE_LOGGING': 'true',
          'ENABLE_DEBUG_TOOLS': 'false',
          'ENABLE_ANALYTICS': 'true',
          'CONNECT_TIMEOUT': '15',
          'CACHE_EXPIRY_MINUTES': '15',
          'ENABLE_MOCK_DATA': 'false',
        };

      case 'production':
      case 'prod':
        return {
          'ENABLE_LOGGING': 'false',
          'ENABLE_DEBUG_TOOLS': 'false',
          'ENABLE_ANALYTICS': 'true',
          'CONNECT_TIMEOUT': '10',
          'CACHE_EXPIRY_MINUTES': '60',
          'ENABLE_MOCK_DATA': 'false',
        };

      default:
        return {};
    }
  }
}
