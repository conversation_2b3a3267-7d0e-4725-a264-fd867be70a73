import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:flutter/foundation.dart';
import '../logging/app_logger.dart';

/// 环境配置管理类
///
/// 提供统一的环境变量访问接口，支持多环境配置
/// 使用 flutter_dotenv 从 .env 文件中读取配置
class EnvironmentConfig {
  /// 初始化环境配置
  ///
  /// 根据构建环境加载对应的 .env 文件
  /// - development: .env.development
  /// - staging: .env.staging
  /// - production: .env.production
  static Future<void> initialize() async {
    const environment = String.fromEnvironment('ENVIRONMENT', defaultValue: 'development');

    String envFile;
    switch (environment.toLowerCase()) {
      case 'production':
      case 'prod':
        envFile = '.env.production';
        break;
      case 'staging':
      case 'stag':
        envFile = '.env.staging';
        break;
      case 'development':
      case 'dev':
      default:
        envFile = '.env.development';
        break;
    }

    try {
      await dotenv.load(fileName: envFile);
      appLogger.info('Environment configuration loaded from $envFile');
    } catch (e) {
      appLogger.warning('Failed to load environment file $envFile: $e');

      // 在Web平台或文件不存在时，使用默认配置
      if (kIsWeb) {
        appLogger.info('Using default configuration for Web platform');
        _initializeDefaultConfig();
      } else {
        rethrow;
      }
    }
  }

  /// 初始化默认配置（用于Web平台或文件加载失败时）
  static void _initializeDefaultConfig() {
    // 设置默认环境变量
    dotenv.env.addAll({
      'API_BASE_URL': 'https://jsonplaceholder.typicode.com',
      'API_VERSION': 'v1',
      'WEBSOCKET_URL': 'wss://echo.websocket.org',
      'CONNECT_TIMEOUT': '10',
      'RECEIVE_TIMEOUT': '15',
      'SEND_TIMEOUT': '10',
      'ENABLE_LOGGING': 'true',
      'ENABLE_DEBUG_TOOLS': 'true',
      'ENABLE_ANALYTICS': 'false',
      'ENABLE_CRASH_REPORTING': 'false',
      'DATABASE_NAME': 'app_dev.db',
      'ENABLE_DATABASE_LOGGING': 'true',
      'CACHE_EXPIRY_MINUTES': '5',
      'MAX_CACHE_SIZE_MB': '50',
      'ENABLE_MOCK_DATA': 'true',
      'MOCK_DELAY_MS': '500',
    });
  }

  // API 配置
  static String get apiBaseUrl {
    if (!dotenv.isInitialized) {
      appLogger.warning('dotenv not initialized, using default API base URL');
      return 'https://jsonplaceholder.typicode.com';
    }
    return dotenv.env['API_BASE_URL'] ?? 'https://jsonplaceholder.typicode.com';
  }

  static String get apiVersion {
    if (!dotenv.isInitialized) return 'v1';
    return dotenv.env['API_VERSION'] ?? 'v1';
  }

  static String get websocketUrl {
    if (!dotenv.isInitialized) return 'wss://echo.websocket.org';
    return dotenv.env['WEBSOCKET_URL'] ?? 'wss://echo.websocket.org';
  }

  // 网络配置
  static Duration get connectTimeout {
    if (!dotenv.isInitialized) return const Duration(seconds: 10);
    return Duration(
      seconds: int.tryParse(dotenv.env['CONNECT_TIMEOUT'] ?? '10') ?? 10,
    );
  }

  static Duration get receiveTimeout {
    if (!dotenv.isInitialized) return const Duration(seconds: 15);
    return Duration(
      seconds: int.tryParse(dotenv.env['RECEIVE_TIMEOUT'] ?? '15') ?? 15,
    );
  }

  static Duration get sendTimeout {
    if (!dotenv.isInitialized) return const Duration(seconds: 10);
    return Duration(
      seconds: int.tryParse(dotenv.env['SEND_TIMEOUT'] ?? '10') ?? 10,
    );
  }

  // 功能开关
  static bool get enableLogging {
    if (!dotenv.isInitialized) return true; // 默认启用日志
    return dotenv.env['ENABLE_LOGGING']?.toLowerCase() == 'true';
  }

  static bool get enableDebugTools {
    if (!dotenv.isInitialized) return true; // 默认启用调试工具
    return dotenv.env['ENABLE_DEBUG_TOOLS']?.toLowerCase() == 'true';
  }

  static bool get enableAnalytics {
    if (!dotenv.isInitialized) return false; // 默认禁用分析
    return dotenv.env['ENABLE_ANALYTICS']?.toLowerCase() == 'true';
  }

  static bool get enableCrashReporting {
    if (!dotenv.isInitialized) return false; // 默认禁用崩溃报告
    return dotenv.env['ENABLE_CRASH_REPORTING']?.toLowerCase() == 'true';
  }

  // 安全配置
  static String get encryptionKey {
    if (!dotenv.isInitialized) return 'dev_encryption_key_12345';
    return dotenv.env['ENCRYPTION_KEY'] ?? 'dev_encryption_key_12345';
  }

  static String get appSecretKey {
    if (!dotenv.isInitialized) return 'dev_app_secret_key_67890';
    return dotenv.env['APP_SECRET_KEY'] ?? 'dev_app_secret_key_67890';
  }

  static bool get enableBiometric {
    if (!dotenv.isInitialized) return true; // 默认启用生物识别
    return dotenv.env['ENABLE_BIOMETRIC']?.toLowerCase() == 'true';
  }

  // 数据库配置
  static String get databaseName {
    if (!dotenv.isInitialized) return 'app_dev.db';
    return dotenv.env['DATABASE_NAME'] ?? 'app_dev.db';
  }

  static bool get enableDatabaseLogging {
    if (!dotenv.isInitialized) return true; // 默认启用数据库日志
    return dotenv.env['ENABLE_DATABASE_LOGGING']?.toLowerCase() == 'true';
  }

  // 缓存配置
  static int get cacheExpiryMinutes {
    if (!dotenv.isInitialized) return 5; // 默认5分钟
    return int.tryParse(dotenv.env['CACHE_EXPIRY_MINUTES'] ?? '5') ?? 5;
  }

  static int get maxCacheSizeMB {
    if (!dotenv.isInitialized) return 50; // 默认50MB
    return int.tryParse(dotenv.env['MAX_CACHE_SIZE_MB'] ?? '50') ?? 50;
  }

  // 测试/Mock 配置
  static bool get enableMockData {
    if (!dotenv.isInitialized) return true; // 默认启用Mock数据
    return dotenv.env['ENABLE_MOCK_DATA']?.toLowerCase() == 'true';
  }

  static int get mockDelayMs {
    if (!dotenv.isInitialized) return 500; // 默认500ms延迟
    return int.tryParse(dotenv.env['MOCK_DELAY_MS'] ?? '500') ?? 500;
  }

  /// 获取当前环境名称
  static String get currentEnvironment =>
    const String.fromEnvironment('ENVIRONMENT', defaultValue: 'development');

  /// 是否为生产环境
  static bool get isProduction =>
    currentEnvironment.toLowerCase() == 'production';

  /// 是否为开发环境
  static bool get isDevelopment =>
    currentEnvironment.toLowerCase() == 'development';

  /// 是否为测试环境
  static bool get isStaging =>
    currentEnvironment.toLowerCase() == 'staging';

  /// 打印当前环境配置（仅在非生产环境）
  static void printConfig() {
    if (kReleaseMode) return;

    appLogger.info('🌍 Environment: $currentEnvironment');
    appLogger.info('🔗 API Base URL: $apiBaseUrl');
    appLogger.info('📡 WebSocket URL: $websocketUrl');
    appLogger.info('🔧 Enable Logging: $enableLogging');
    appLogger.info('🛠️ Enable Debug Tools: $enableDebugTools');
    appLogger.info('📊 Enable Analytics: $enableAnalytics');
    appLogger.info('💾 Database: $databaseName');
    appLogger.info('🗄️ Cache Expiry: ${cacheExpiryMinutes}min');
  }
}
