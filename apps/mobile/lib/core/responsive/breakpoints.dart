import 'package:flutter/material.dart';

/// 屏幕类型枚举
enum ScreenType {
  /// 移动端 (0-600px)
  mobile,
  
  /// 平板端 (600-1024px)
  tablet,
  
  /// 桌面端 (1024-1440px)
  desktop,
  
  /// 电视端 (1440px+)
  tv,
}

/// 响应式断点定义
class Breakpoints {
  const Breakpoints._();

  /// 移动端断点 (0-600px)
  static const double mobile = 600;
  
  /// 平板端断点 (600-1024px)
  static const double tablet = 1024;
  
  /// 桌面端断点 (1024-1440px)
  static const double desktop = 1440;
  
  /// 电视端断点 (1440px+)
  static const double tv = 1920;

  /// 根据屏幕宽度获取屏幕类型
  static ScreenType getScreenType(double width) {
    if (width < mobile) {
      return ScreenType.mobile;
    } else if (width < tablet) {
      return ScreenType.tablet;
    } else if (width < desktop) {
      return ScreenType.desktop;
    } else {
      return ScreenType.tv;
    }
  }

  /// 判断是否为移动端
  static bool isMobile(double width) => width < mobile;

  /// 判断是否为平板端
  static bool isTablet(double width) => width >= mobile && width < tablet;

  /// 判断是否为桌面端
  static bool isDesktop(double width) => width >= tablet && width < desktop;

  /// 判断是否为电视端
  static bool isTV(double width) => width >= desktop;

  /// 判断是否为小屏设备 (移动端)
  static bool isSmallScreen(double width) => width < mobile;

  /// 判断是否为中等屏幕 (平板端)
  static bool isMediumScreen(double width) => width >= mobile && width < tablet;

  /// 判断是否为大屏设备 (桌面端及以上)
  static bool isLargeScreen(double width) => width >= tablet;
}

/// 屏幕类型扩展方法
extension ScreenTypeExtension on ScreenType {
  /// 屏幕类型显示名称
  String get displayName {
    switch (this) {
      case ScreenType.mobile:
        return '移动端';
      case ScreenType.tablet:
        return '平板端';
      case ScreenType.desktop:
        return '桌面端';
      case ScreenType.tv:
        return '电视端';
    }
  }

  /// 屏幕类型图标
  IconData get icon {
    switch (this) {
      case ScreenType.mobile:
        return Icons.smartphone;
      case ScreenType.tablet:
        return Icons.tablet;
      case ScreenType.desktop:
        return Icons.desktop_windows;
      case ScreenType.tv:
        return Icons.tv;
    }
  }

  /// 是否为移动端
  bool get isMobile => this == ScreenType.mobile;

  /// 是否为平板端
  bool get isTablet => this == ScreenType.tablet;

  /// 是否为桌面端
  bool get isDesktop => this == ScreenType.desktop;

  /// 是否为电视端
  bool get isTV => this == ScreenType.tv;

  /// 是否为小屏设备
  bool get isSmallScreen => this == ScreenType.mobile;

  /// 是否为中等屏幕
  bool get isMediumScreen => this == ScreenType.tablet;

  /// 是否为大屏设备
  bool get isLargeScreen => this == ScreenType.desktop || this == ScreenType.tv;

  /// 获取推荐的列数
  int get recommendedColumns {
    switch (this) {
      case ScreenType.mobile:
        return 1;
      case ScreenType.tablet:
        return 2;
      case ScreenType.desktop:
        return 3;
      case ScreenType.tv:
        return 4;
    }
  }

  /// 获取推荐的内边距
  EdgeInsets get recommendedPadding {
    switch (this) {
      case ScreenType.mobile:
        return const EdgeInsets.all(16);
      case ScreenType.tablet:
        return const EdgeInsets.all(24);
      case ScreenType.desktop:
        return const EdgeInsets.all(32);
      case ScreenType.tv:
        return const EdgeInsets.all(48);
    }
  }

  /// 获取推荐的间距
  double get recommendedSpacing {
    switch (this) {
      case ScreenType.mobile:
        return 8;
      case ScreenType.tablet:
        return 12;
      case ScreenType.desktop:
        return 16;
      case ScreenType.tv:
        return 24;
    }
  }

  /// 获取推荐的最大内容宽度
  double? get maxContentWidth {
    switch (this) {
      case ScreenType.mobile:
        return null; // 无限制
      case ScreenType.tablet:
        return 800;
      case ScreenType.desktop:
        return 1200;
      case ScreenType.tv:
        return 1600;
    }
  }
}
