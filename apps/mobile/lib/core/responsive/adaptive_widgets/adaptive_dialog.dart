import 'package:flutter/material.dart';
import '../breakpoints.dart';
import '../responsive_builder.dart';
import '../../theme/theme_extensions.dart';

/// 自适应对话框
class AdaptiveDialog extends StatelessWidget {
  /// 对话框标题
  final Widget? title;

  /// 对话框内容
  final Widget? content;

  /// 操作按钮列表
  final List<Widget>? actions;

  /// 是否可以通过点击外部关闭
  final bool barrierDismissible;

  /// 内容内边距
  final EdgeInsets? contentPadding;

  /// 操作按钮内边距
  final EdgeInsets? actionsPadding;

  /// 操作按钮对齐方式
  final MainAxisAlignment? actionsAlignment;

  const AdaptiveDialog({
    super.key,
    this.title,
    this.content,
    this.actions,
    this.barrierDismissible = true,
    this.contentPadding,
    this.actionsPadding,
    this.actionsAlignment,
  });

  @override
  Widget build(BuildContext context) {
    return ResponsiveBuilder(
      builder: (context, screenType, width, height) {
        switch (screenType) {
          case ScreenType.mobile:
            return _buildMobileDialog(context);

          case ScreenType.tablet:
            return _buildTabletDialog(context);

          case ScreenType.desktop:
          case ScreenType.tv:
            return _buildDesktopDialog(context);
        }
      },
    );
  }

  /// 构建移动端对话框（全屏或接近全屏）
  Widget _buildMobileDialog(BuildContext context) {
    return AlertDialog(
      title: title,
      content: content,
      actions: actions,
      contentPadding: contentPadding ?? const EdgeInsets.fromLTRB(24, 20, 24, 24),
      actionsPadding: actionsPadding ?? const EdgeInsets.fromLTRB(24, 0, 24, 24),
      actionsAlignment: actionsAlignment ?? MainAxisAlignment.end,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
    );
  }

  /// 构建平板端对话框（中等尺寸）
  Widget _buildTabletDialog(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20),
      ),
      child: ConstrainedBox(
        constraints: const BoxConstraints(
          maxWidth: 500,
          maxHeight: 600,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            if (title != null) ...[
              Padding(
                padding: const EdgeInsets.fromLTRB(24, 24, 24, 16),
                child: DefaultTextStyle(
                  style: context.textStyles.heading5!,
                  child: title!,
                ),
              ),
            ],
            if (content != null) ...[
              Flexible(
                child: Padding(
                  padding: contentPadding ?? const EdgeInsets.fromLTRB(24, 0, 24, 24),
                  child: content,
                ),
              ),
            ],
            if (actions != null && actions!.isNotEmpty) ...[
              Padding(
                padding: actionsPadding ?? const EdgeInsets.fromLTRB(24, 0, 24, 24),
                child: Row(
                  mainAxisAlignment: actionsAlignment ?? MainAxisAlignment.end,
                  children: actions!,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// 构建桌面端对话框（小尺寸，居中）
  Widget _buildDesktopDialog(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: ConstrainedBox(
        constraints: const BoxConstraints(
          maxWidth: 400,
          maxHeight: 500,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            if (title != null) ...[
              Padding(
                padding: const EdgeInsets.fromLTRB(20, 20, 20, 12),
                child: DefaultTextStyle(
                  style: context.textStyles.heading6!,
                  child: title!,
                ),
              ),
            ],
            if (content != null) ...[
              Flexible(
                child: Padding(
                  padding: contentPadding ?? const EdgeInsets.fromLTRB(20, 0, 20, 20),
                  child: content!,
                ),
              ),
            ],
            if (actions != null && actions!.isNotEmpty) ...[
              Padding(
                padding: actionsPadding ?? const EdgeInsets.fromLTRB(20, 0, 20, 20),
                child: Row(
                  mainAxisAlignment: actionsAlignment ?? MainAxisAlignment.end,
                  children: actions!,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// 显示自适应对话框
  static Future<T?> show<T>({
    required BuildContext context,
    Widget? title,
    Widget? content,
    List<Widget>? actions,
    bool barrierDismissible = true,
    EdgeInsets? contentPadding,
    EdgeInsets? actionsPadding,
    MainAxisAlignment? actionsAlignment,
  }) {
    return showDialog<T>(
      context: context,
      barrierDismissible: barrierDismissible,
      builder: (context) => AdaptiveDialog(
        title: title,
        content: content,
        actions: actions,
        barrierDismissible: barrierDismissible,
        contentPadding: contentPadding,
        actionsPadding: actionsPadding,
        actionsAlignment: actionsAlignment,
      ),
    );
  }
}

/// 自适应确认对话框
class AdaptiveConfirmDialog extends StatelessWidget {
  /// 对话框标题
  final String title;

  /// 对话框内容
  final String content;

  /// 确认按钮文本
  final String confirmText;

  /// 取消按钮文本
  final String cancelText;

  /// 确认按钮是否为危险操作
  final bool isDestructive;

  const AdaptiveConfirmDialog({
    super.key,
    required this.title,
    required this.content,
    this.confirmText = '确认',
    this.cancelText = '取消',
    this.isDestructive = false,
  });

  @override
  Widget build(BuildContext context) {
    return AdaptiveDialog(
      title: Text(title),
      content: Text(content),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(false),
          child: Text(cancelText),
        ),
        const SizedBox(width: 8),
        FilledButton(
          onPressed: () => Navigator.of(context).pop(true),
          style: isDestructive
              ? FilledButton.styleFrom(
                  backgroundColor: context.colors.error,
                  foregroundColor: context.colors.onError,
                )
              : null,
          child: Text(confirmText),
        ),
      ],
    );
  }

  /// 显示确认对话框
  static Future<bool> show({
    required BuildContext context,
    required String title,
    required String content,
    String confirmText = '确认',
    String cancelText = '取消',
    bool isDestructive = false,
  }) async {
    final result = await AdaptiveDialog.show<bool>(
      context: context,
      title: Text(title),
      content: Text(content),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(false),
          child: Text(cancelText),
        ),
        const SizedBox(width: 8),
        FilledButton(
          onPressed: () => Navigator.of(context).pop(true),
          style: isDestructive
              ? FilledButton.styleFrom(
                  backgroundColor: context.colors.error,
                  foregroundColor: context.colors.onError,
                )
              : null,
          child: Text(confirmText),
        ),
      ],
    );

    return result ?? false;
  }
}

/// 自适应加载对话框
class AdaptiveLoadingDialog extends StatelessWidget {
  /// 加载提示文本
  final String? message;

  /// 是否可以取消
  final bool cancellable;

  const AdaptiveLoadingDialog({
    super.key,
    this.message,
    this.cancellable = false,
  });

  @override
  Widget build(BuildContext context) {
    return AdaptiveDialog(
      barrierDismissible: cancellable,
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const CircularProgressIndicator(),
          if (message != null) ...[
            const SizedBox(height: 16),
            Text(
              message!,
              textAlign: TextAlign.center,
              style: context.textStyles.body,
            ),
          ],
        ],
      ),
      actions: cancellable
          ? [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('取消'),
              ),
            ]
          : null,
    );
  }

  /// 显示加载对话框
  static Future<T?> show<T>({
    required BuildContext context,
    String? message,
    bool cancellable = false,
  }) {
    return AdaptiveDialog.show<T>(
      context: context,
      barrierDismissible: cancellable,
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const CircularProgressIndicator(),
          if (message != null) ...[
            const SizedBox(height: 16),
            Text(
              message,
              textAlign: TextAlign.center,
            ),
          ],
        ],
      ),
      actions: cancellable
          ? [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('取消'),
              ),
            ]
          : null,
    );
  }
}
