import 'package:flutter/material.dart';
import '../breakpoints.dart';
import '../responsive_builder.dart';
import '../../theme/theme_extensions.dart';

/// 自适应卡片
class AdaptiveCard extends StatelessWidget {
  /// 卡片内容
  final Widget child;

  /// 点击回调
  final VoidCallback? onTap;

  /// 长按回调
  final VoidCallback? onLongPress;

  /// 响应式内边距
  final ResponsiveValue<EdgeInsets>? padding;

  /// 响应式外边距
  final ResponsiveValue<EdgeInsets>? margin;

  /// 响应式圆角半径
  final ResponsiveValue<double>? borderRadius;

  /// 响应式阴影高度
  final ResponsiveValue<double>? elevation;

  /// 背景颜色
  final Color? backgroundColor;

  /// 是否显示边框
  final bool showBorder;

  /// 边框颜色
  final Color? borderColor;

  const AdaptiveCard({
    super.key,
    required this.child,
    this.onTap,
    this.onLongPress,
    this.padding,
    this.margin,
    this.borderRadius,
    this.elevation,
    this.backgroundColor,
    this.showBorder = false,
    this.borderColor,
  });

  @override
  Widget build(BuildContext context) {
    return ResponsiveBuilder(
      builder: (context, screenType, width, height) {
        final defaultPadding = EdgeInsets.all(screenType.recommendedSpacing);
        final defaultMargin = EdgeInsets.all(screenType.recommendedSpacing / 2);
        final defaultBorderRadius = screenType.isMobile ? 12.0 : 16.0;
        final defaultElevation = screenType.isMobile ? 2.0 : 4.0;

        final cardPadding = padding?.getValue(screenType) ?? defaultPadding;
        final cardMargin = margin?.getValue(screenType) ?? defaultMargin;
        final cardBorderRadius = borderRadius?.getValue(screenType) ?? defaultBorderRadius;
        final cardElevation = elevation?.getValue(screenType) ?? defaultElevation;

        Widget card = Card(
          margin: cardMargin,
          elevation: cardElevation,
          color: backgroundColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(cardBorderRadius),
            side: showBorder
                ? BorderSide(
                    color: borderColor ?? context.colors.outline,
                    width: 1,
                  )
                : BorderSide.none,
          ),
          child: Padding(
            padding: cardPadding,
            child: child,
          ),
        );

        if (onTap != null || onLongPress != null) {
          card = InkWell(
            onTap: onTap,
            onLongPress: onLongPress,
            borderRadius: BorderRadius.circular(cardBorderRadius),
            child: card,
          );
        }

        return card;
      },
    );
  }
}

/// 自适应列表项
class AdaptiveListTile extends StatelessWidget {
  /// 前导图标
  final Widget? leading;

  /// 标题
  final Widget? title;

  /// 副标题
  final Widget? subtitle;

  /// 尾随图标
  final Widget? trailing;

  /// 点击回调
  final VoidCallback? onTap;

  /// 长按回调
  final VoidCallback? onLongPress;

  /// 是否选中
  final bool selected;

  /// 是否启用
  final bool enabled;

  /// 内容内边距
  final EdgeInsets? contentPadding;

  /// 是否密集布局
  final bool? dense;

  const AdaptiveListTile({
    super.key,
    this.leading,
    this.title,
    this.subtitle,
    this.trailing,
    this.onTap,
    this.onLongPress,
    this.selected = false,
    this.enabled = true,
    this.contentPadding,
    this.dense,
  });

  @override
  Widget build(BuildContext context) {
    return ResponsiveBuilder(
      builder: (context, screenType, width, height) {
        final defaultPadding = EdgeInsets.symmetric(
          horizontal: screenType.recommendedSpacing,
          vertical: screenType.recommendedSpacing / 2,
        );

        final tilePadding = contentPadding ?? defaultPadding;
        final isDense = dense ?? screenType.isMobile;

        return ListTile(
          leading: leading,
          title: title,
          subtitle: subtitle,
          trailing: trailing,
          onTap: enabled ? onTap : null,
          onLongPress: enabled ? onLongPress : null,
          selected: selected,
          enabled: enabled,
          contentPadding: tilePadding,
          dense: isDense,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(
              screenType.isMobile ? 8.0 : 12.0,
            ),
          ),
        );
      },
    );
  }
}

/// 自适应网格卡片
class AdaptiveGridCard extends StatelessWidget {
  /// 卡片内容
  final Widget child;

  /// 点击回调
  final VoidCallback? onTap;

  /// 卡片标题
  final String? title;

  /// 卡片副标题
  final String? subtitle;

  /// 卡片图片
  final Widget? image;

  /// 操作按钮
  final List<Widget>? actions;

  const AdaptiveGridCard({
    super.key,
    required this.child,
    this.onTap,
    this.title,
    this.subtitle,
    this.image,
    this.actions,
  });

  @override
  Widget build(BuildContext context) {
    return ResponsiveBuilder(
      builder: (context, screenType, width, height) {
        final borderRadius = screenType.isMobile ? 12.0 : 16.0;

        return Card(
          elevation: screenType.isMobile ? 2.0 : 4.0,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(borderRadius),
          ),
          child: InkWell(
            onTap: onTap,
            borderRadius: BorderRadius.circular(borderRadius),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                // 图片部分
                if (image != null) ...[
                  ClipRRect(
                    borderRadius: BorderRadius.vertical(
                      top: Radius.circular(borderRadius),
                    ),
                    child: AspectRatio(
                      aspectRatio: screenType.isMobile ? 16 / 9 : 4 / 3,
                      child: image!,
                    ),
                  ),
                ],

                // 内容部分
                Expanded(
                  child: Padding(
                    padding: EdgeInsets.all(screenType.recommendedSpacing),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // 标题和副标题
                        if (title != null) ...[
                          Text(
                            title!,
                            style: context.textStyles.subtitle,
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                          if (subtitle != null) ...[
                            const SizedBox(height: 4),
                            Text(
                              subtitle!,
                              style: context.textStyles.caption,
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ],
                          const SizedBox(height: 8),
                        ],

                        // 自定义内容
                        Expanded(child: child),

                        // 操作按钮
                        if (actions != null && actions!.isNotEmpty) ...[
                          const SizedBox(height: 8),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.end,
                            children: actions!,
                          ),
                        ],
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}

/// 自适应信息卡片
class AdaptiveInfoCard extends StatelessWidget {
  /// 图标
  final IconData icon;

  /// 标题
  final String title;

  /// 数值
  final String value;

  /// 副标题
  final String? subtitle;

  /// 点击回调
  final VoidCallback? onTap;

  /// 图标颜色
  final Color? iconColor;

  /// 背景颜色
  final Color? backgroundColor;

  const AdaptiveInfoCard({
    super.key,
    required this.icon,
    required this.title,
    required this.value,
    this.subtitle,
    this.onTap,
    this.iconColor,
    this.backgroundColor,
  });

  @override
  Widget build(BuildContext context) {
    return ResponsiveBuilder(
      builder: (context, screenType, width, height) {
        final colorScheme = context.colors;
        final textTheme = context.textStyles;

        return AdaptiveCard(
          onTap: onTap,
          backgroundColor: backgroundColor,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(
                    icon,
                    color: iconColor ?? colorScheme.primary,
                    size: screenType.isMobile ? 24 : 32,
                  ),
                  const Spacer(),
                  if (onTap != null)
                    Icon(
                      Icons.arrow_forward_ios,
                      size: 16,
                      color: colorScheme.onSurfaceVariant,
                    ),
                ],
              ),

              SizedBox(height: screenType.recommendedSpacing),

              Text(
                value,
                style: screenType.isMobile
                    ? textTheme.headlineMedium
                    : textTheme.headlineLarge,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),

              const SizedBox(height: 4),

              Text(
                title,
                style: textTheme.titleMedium?.copyWith(
                  color: colorScheme.onSurfaceVariant,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),

              if (subtitle != null) ...[
                const SizedBox(height: 4),
                Text(
                  subtitle!,
                  style: textTheme.bodySmall?.copyWith(
                    color: colorScheme.onSurfaceVariant,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ],
          ),
        );
      },
    );
  }
}
