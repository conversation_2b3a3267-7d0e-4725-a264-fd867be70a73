import 'package:flutter/material.dart';
import '../breakpoints.dart';
import '../responsive_builder.dart';

/// 导航目标项
class AdaptiveNavigationDestination {
  /// 图标
  final IconData icon;

  /// 选中时的图标
  final IconData? selectedIcon;

  /// 标签
  final String label;

  /// 徽章文本
  final String? badge;

  /// 工具提示
  final String? tooltip;

  const AdaptiveNavigationDestination({
    required this.icon,
    this.selectedIcon,
    required this.label,
    this.badge,
    this.tooltip,
  });
}

/// 自适应导航组件
class AdaptiveNavigation extends StatelessWidget {
  /// 导航目标列表
  final List<AdaptiveNavigationDestination> destinations;

  /// 当前选中的索引
  final int selectedIndex;

  /// 选中回调
  final ValueChanged<int>? onDestinationSelected;

  /// 是否显示标签
  final bool showLabels;

  /// 导航类型（自动适配或强制指定）
  final AdaptiveNavigationType? type;

  /// 扩展状态（仅对NavigationRail有效）
  final bool extended;

  const AdaptiveNavigation({
    super.key,
    required this.destinations,
    required this.selectedIndex,
    this.onDestinationSelected,
    this.showLabels = true,
    this.type,
    this.extended = false,
  });

  @override
  Widget build(BuildContext context) {
    return ResponsiveBuilder(
      builder: (context, screenType, width, height) {
        final navigationType = type ?? _getDefaultNavigationType(screenType);

        switch (navigationType) {
          case AdaptiveNavigationType.bottomNavigation:
            return _buildBottomNavigation(context);

          case AdaptiveNavigationType.navigationRail:
            return _buildNavigationRail(context);

          case AdaptiveNavigationType.navigationDrawer:
            return _buildNavigationDrawer(context);

          case AdaptiveNavigationType.tabs:
            return _buildTabBar(context);
        }
      },
    );
  }

  /// 根据屏幕类型获取默认导航类型
  AdaptiveNavigationType _getDefaultNavigationType(ScreenType screenType) {
    switch (screenType) {
      case ScreenType.mobile:
        return AdaptiveNavigationType.bottomNavigation;
      case ScreenType.tablet:
        return AdaptiveNavigationType.navigationRail;
      case ScreenType.desktop:
      case ScreenType.tv:
        return AdaptiveNavigationType.navigationDrawer;
    }
  }

  /// 构建底部导航栏
  Widget _buildBottomNavigation(BuildContext context) {
    return BottomNavigationBar(
      currentIndex: selectedIndex,
      onTap: onDestinationSelected,
      type: destinations.length > 3
          ? BottomNavigationBarType.shifting
          : BottomNavigationBarType.fixed,
      showSelectedLabels: showLabels,
      showUnselectedLabels: showLabels,
      items: destinations.map((destination) {
        return BottomNavigationBarItem(
          icon: _buildIcon(destination.icon, destination.badge),
          activeIcon: _buildIcon(
            destination.selectedIcon ?? destination.icon,
            destination.badge,
          ),
          label: destination.label,
          tooltip: destination.tooltip,
        );
      }).toList(),
    );
  }

  /// 构建导航栏
  Widget _buildNavigationRail(BuildContext context) {
    return NavigationRail(
      selectedIndex: selectedIndex,
      onDestinationSelected: onDestinationSelected,
      extended: extended,
      labelType: showLabels
          ? NavigationRailLabelType.all
          : NavigationRailLabelType.none,
      destinations: destinations.map((destination) {
        return NavigationRailDestination(
          icon: _buildIcon(destination.icon, destination.badge),
          selectedIcon: _buildIcon(
            destination.selectedIcon ?? destination.icon,
            destination.badge,
          ),
          label: Text(destination.label),
        );
      }).toList(),
    );
  }

  /// 构建导航抽屉
  Widget _buildNavigationDrawer(BuildContext context) {
    return NavigationDrawer(
      selectedIndex: selectedIndex,
      onDestinationSelected: onDestinationSelected,
      children: [
        const SizedBox(height: 16),
        ...destinations.map((destination) {
          return NavigationDrawerDestination(
            icon: _buildIcon(destination.icon, destination.badge),
            selectedIcon: _buildIcon(
              destination.selectedIcon ?? destination.icon,
              destination.badge,
            ),
            label: Text(destination.label),
          );
        }),
      ],
    );
  }

  /// 构建标签栏
  Widget _buildTabBar(BuildContext context) {
    return TabBar(
      tabs: destinations.map((destination) {
        return Tab(
          icon: _buildIcon(destination.icon, destination.badge),
          text: showLabels ? destination.label : null,
        );
      }).toList(),
    );
  }

  /// 构建图标（包含徽章）
  Widget _buildIcon(IconData iconData, String? badge) {
    final icon = Icon(iconData);

    if (badge == null || badge.isEmpty) {
      return icon;
    }

    return Badge(
      label: Text(badge),
      child: icon,
    );
  }
}

/// 自适应导航类型
enum AdaptiveNavigationType {
  /// 底部导航栏
  bottomNavigation,

  /// 导航栏（侧边）
  navigationRail,

  /// 导航抽屉
  navigationDrawer,

  /// 标签栏
  tabs,
}

/// 自适应导航脚手架
class AdaptiveNavigationScaffold extends StatelessWidget {
  /// 导航目标列表
  final List<AdaptiveNavigationDestination> destinations;

  /// 当前选中的索引
  final int selectedIndex;

  /// 选中回调
  final ValueChanged<int>? onDestinationSelected;

  /// 页面构建器
  final Widget Function(BuildContext context, int index) pageBuilder;

  /// 应用栏
  final PreferredSizeWidget? appBar;

  /// 浮动操作按钮
  final Widget? floatingActionButton;

  /// 是否显示导航标签
  final bool showLabels;

  const AdaptiveNavigationScaffold({
    super.key,
    required this.destinations,
    required this.selectedIndex,
    required this.pageBuilder,
    this.onDestinationSelected,
    this.appBar,
    this.floatingActionButton,
    this.showLabels = true,
  });

  @override
  Widget build(BuildContext context) {
    return ResponsiveBuilder(
      builder: (context, screenType, width, height) {
        final body = pageBuilder(context, selectedIndex);

        switch (screenType) {
          case ScreenType.mobile:
            return Scaffold(
              appBar: appBar,
              body: body,
              bottomNavigationBar: AdaptiveNavigation(
                destinations: destinations,
                selectedIndex: selectedIndex,
                onDestinationSelected: onDestinationSelected,
                showLabels: showLabels,
                type: AdaptiveNavigationType.bottomNavigation,
              ),
              floatingActionButton: floatingActionButton,
            );

          case ScreenType.tablet:
            return Scaffold(
              appBar: appBar,
              body: Row(
                children: [
                  AdaptiveNavigation(
                    destinations: destinations,
                    selectedIndex: selectedIndex,
                    onDestinationSelected: onDestinationSelected,
                    showLabels: showLabels,
                    type: AdaptiveNavigationType.navigationRail,
                  ),
                  const VerticalDivider(width: 1),
                  Expanded(child: body),
                ],
              ),
              floatingActionButton: floatingActionButton,
            );

          case ScreenType.desktop:
          case ScreenType.tv:
            return Scaffold(
              appBar: appBar,
              drawer: AdaptiveNavigation(
                destinations: destinations,
                selectedIndex: selectedIndex,
                onDestinationSelected: onDestinationSelected,
                showLabels: showLabels,
                type: AdaptiveNavigationType.navigationDrawer,
              ),
              body: body,
              floatingActionButton: floatingActionButton,
            );
        }
      },
    );
  }
}
