import 'package:flutter/material.dart';
import 'breakpoints.dart';

/// 响应式构建器回调函数类型
typedef ResponsiveWidgetBuilder = Widget Function(
  BuildContext context,
  ScreenType screenType,
  double width,
  double height,
);

/// 响应式布局构建器
class ResponsiveBuilder extends StatelessWidget {
  /// 移动端构建器
  final WidgetBuilder? mobile;
  
  /// 平板端构建器
  final WidgetBuilder? tablet;
  
  /// 桌面端构建器
  final WidgetBuilder? desktop;
  
  /// 电视端构建器
  final WidgetBuilder? tv;
  
  /// 通用构建器（当特定屏幕类型构建器不存在时使用）
  final ResponsiveWidgetBuilder? builder;

  const ResponsiveBuilder({
    super.key,
    this.mobile,
    this.tablet,
    this.desktop,
    this.tv,
    this.builder,
  }) : assert(
          mobile != null || 
          tablet != null || 
          desktop != null || 
          tv != null || 
          builder != null,
          'At least one builder must be provided',
        );

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final width = constraints.maxWidth;
        final height = constraints.maxHeight;
        final screenType = Breakpoints.getScreenType(width);

        // 优先使用特定屏幕类型的构建器
        switch (screenType) {
          case ScreenType.mobile:
            if (mobile != null) return mobile!(context);
            break;
          case ScreenType.tablet:
            if (tablet != null) return tablet!(context);
            break;
          case ScreenType.desktop:
            if (desktop != null) return desktop!(context);
            break;
          case ScreenType.tv:
            if (tv != null) return tv!(context);
            break;
        }

        // 降级策略：使用通用构建器
        if (builder != null) {
          return builder!(context, screenType, width, height);
        }

        // 最终降级：使用可用的构建器
        final fallbackBuilder = mobile ?? tablet ?? desktop ?? tv;
        if (fallbackBuilder != null) {
          return fallbackBuilder(context);
        }

        // 如果没有任何构建器，返回错误提示
        return const Center(
          child: Text('No builder provided for current screen type'),
        );
      },
    );
  }
}

/// 响应式值选择器
class ResponsiveValue<T> {
  /// 移动端值
  final T? mobile;
  
  /// 平板端值
  final T? tablet;
  
  /// 桌面端值
  final T? desktop;
  
  /// 电视端值
  final T? tv;
  
  /// 默认值
  final T defaultValue;

  const ResponsiveValue({
    this.mobile,
    this.tablet,
    this.desktop,
    this.tv,
    required this.defaultValue,
  });

  /// 根据屏幕类型获取对应的值
  T getValue(ScreenType screenType) {
    switch (screenType) {
      case ScreenType.mobile:
        return mobile ?? defaultValue;
      case ScreenType.tablet:
        return tablet ?? mobile ?? defaultValue;
      case ScreenType.desktop:
        return desktop ?? tablet ?? mobile ?? defaultValue;
      case ScreenType.tv:
        return tv ?? desktop ?? tablet ?? mobile ?? defaultValue;
    }
  }

  /// 根据屏幕宽度获取对应的值
  T getValueForWidth(double width) {
    final screenType = Breakpoints.getScreenType(width);
    return getValue(screenType);
  }
}

/// 响应式扩展方法
extension ResponsiveExtensions on BuildContext {
  /// 获取当前屏幕类型
  ScreenType get screenType {
    final width = MediaQuery.of(this).size.width;
    return Breakpoints.getScreenType(width);
  }

  /// 获取当前屏幕宽度
  double get screenWidth => MediaQuery.of(this).size.width;

  /// 获取当前屏幕高度
  double get screenHeight => MediaQuery.of(this).size.height;

  /// 判断是否为移动端
  bool get isMobile => screenType.isMobile;

  /// 判断是否为平板端
  bool get isTablet => screenType.isTablet;

  /// 判断是否为桌面端
  bool get isDesktop => screenType.isDesktop;

  /// 判断是否为电视端
  bool get isTV => screenType.isTV;

  /// 判断是否为小屏设备
  bool get isSmallScreen => screenType.isSmallScreen;

  /// 判断是否为中等屏幕
  bool get isMediumScreen => screenType.isMediumScreen;

  /// 判断是否为大屏设备
  bool get isLargeScreen => screenType.isLargeScreen;

  /// 获取响应式值
  T responsiveValue<T>(ResponsiveValue<T> responsiveValue) {
    return responsiveValue.getValue(screenType);
  }

  /// 根据屏幕类型执行不同的操作
  T when<T>({
    T Function()? mobile,
    T Function()? tablet,
    T Function()? desktop,
    T Function()? tv,
    required T Function() orElse,
  }) {
    switch (screenType) {
      case ScreenType.mobile:
        return mobile?.call() ?? orElse();
      case ScreenType.tablet:
        return tablet?.call() ?? orElse();
      case ScreenType.desktop:
        return desktop?.call() ?? orElse();
      case ScreenType.tv:
        return tv?.call() ?? orElse();
    }
  }
}

/// 响应式间距辅助类
class ResponsiveSpacing {
  const ResponsiveSpacing._();

  /// 获取响应式内边距
  static EdgeInsets getPadding(ScreenType screenType) {
    return screenType.recommendedPadding;
  }

  /// 获取响应式间距
  static double getSpacing(ScreenType screenType) {
    return screenType.recommendedSpacing;
  }

  /// 获取响应式列数
  static int getColumns(ScreenType screenType) {
    return screenType.recommendedColumns;
  }

  /// 获取响应式最大内容宽度
  static double? getMaxContentWidth(ScreenType screenType) {
    return screenType.maxContentWidth;
  }
}

/// 响应式网格辅助类
class ResponsiveGrid {
  const ResponsiveGrid._();

  /// 计算响应式网格的交叉轴数量
  static int getCrossAxisCount(double width, {
    int mobileColumns = 1,
    int tabletColumns = 2,
    int desktopColumns = 3,
    int tvColumns = 4,
  }) {
    final screenType = Breakpoints.getScreenType(width);
    switch (screenType) {
      case ScreenType.mobile:
        return mobileColumns;
      case ScreenType.tablet:
        return tabletColumns;
      case ScreenType.desktop:
        return desktopColumns;
      case ScreenType.tv:
        return tvColumns;
    }
  }

  /// 计算响应式网格的子项宽高比
  static double getChildAspectRatio(double width, {
    double mobileRatio = 1.0,
    double tabletRatio = 1.2,
    double desktopRatio = 1.5,
    double tvRatio = 1.8,
  }) {
    final screenType = Breakpoints.getScreenType(width);
    switch (screenType) {
      case ScreenType.mobile:
        return mobileRatio;
      case ScreenType.tablet:
        return tabletRatio;
      case ScreenType.desktop:
        return desktopRatio;
      case ScreenType.tv:
        return tvRatio;
    }
  }
}
