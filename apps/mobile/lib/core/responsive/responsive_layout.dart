import 'package:flutter/material.dart';
import 'breakpoints.dart';
import 'responsive_builder.dart';

/// 响应式布局基类
abstract class ResponsiveLayout extends StatelessWidget {
  const ResponsiveLayout({super.key});

  /// 移动端布局
  Widget buildMobile(BuildContext context);

  /// 平板端布局（可选，默认使用移动端布局）
  Widget buildTablet(BuildContext context) => buildMobile(context);

  /// 桌面端布局（可选，默认使用平板端布局）
  Widget buildDesktop(BuildContext context) => buildTablet(context);

  /// 电视端布局（可选，默认使用桌面端布局）
  Widget buildTV(BuildContext context) => buildDesktop(context);

  @override
  Widget build(BuildContext context) {
    return ResponsiveBuilder(
      mobile: (context) => buildMobile(context),
      tablet: (context) => buildTablet(context),
      desktop: (context) => buildDesktop(context),
      tv: (context) => buildTV(context),
    );
  }
}

/// 响应式页面布局
class ResponsivePage extends StatelessWidget {
  /// 页面标题
  final String? title;
  
  /// 应用栏
  final PreferredSizeWidget? appBar;
  
  /// 页面内容
  final Widget child;
  
  /// 是否显示应用栏
  final bool showAppBar;
  
  /// 是否使用安全区域
  final bool useSafeArea;
  
  /// 背景颜色
  final Color? backgroundColor;
  
  /// 浮动操作按钮
  final Widget? floatingActionButton;
  
  /// 底部导航栏
  final Widget? bottomNavigationBar;
  
  /// 抽屉
  final Widget? drawer;
  
  /// 结束抽屉
  final Widget? endDrawer;

  const ResponsivePage({
    super.key,
    this.title,
    this.appBar,
    required this.child,
    this.showAppBar = true,
    this.useSafeArea = true,
    this.backgroundColor,
    this.floatingActionButton,
    this.bottomNavigationBar,
    this.drawer,
    this.endDrawer,
  });

  @override
  Widget build(BuildContext context) {
    return ResponsiveBuilder(
      builder: (context, screenType, width, height) {
        final content = _buildContent(context, screenType);
        
        return Scaffold(
          appBar: _buildAppBar(context, screenType),
          body: useSafeArea ? SafeArea(child: content) : content,
          backgroundColor: backgroundColor,
          floatingActionButton: floatingActionButton,
          bottomNavigationBar: _buildBottomNavigationBar(context, screenType),
          drawer: _buildDrawer(context, screenType),
          endDrawer: endDrawer,
        );
      },
    );
  }

  /// 构建应用栏
  PreferredSizeWidget? _buildAppBar(BuildContext context, ScreenType screenType) {
    if (!showAppBar) return null;
    
    if (appBar != null) return appBar;
    
    if (title != null) {
      return AppBar(
        title: Text(title!),
        centerTitle: screenType.isMobile,
      );
    }
    
    return null;
  }

  /// 构建页面内容
  Widget _buildContent(BuildContext context, ScreenType screenType) {
    final maxWidth = screenType.maxContentWidth;
    final padding = screenType.recommendedPadding;
    
    Widget content = Padding(
      padding: padding,
      child: child,
    );
    
    // 在大屏设备上限制最大宽度
    if (maxWidth != null && screenType.isLargeScreen) {
      content = Center(
        child: ConstrainedBox(
          constraints: BoxConstraints(maxWidth: maxWidth),
          child: content,
        ),
      );
    }
    
    return content;
  }

  /// 构建底部导航栏
  Widget? _buildBottomNavigationBar(BuildContext context, ScreenType screenType) {
    // 在桌面端隐藏底部导航栏
    if (screenType.isLargeScreen) return null;
    
    return bottomNavigationBar;
  }

  /// 构建抽屉
  Widget? _buildDrawer(BuildContext context, ScreenType screenType) {
    // 在桌面端可能不需要抽屉
    if (screenType.isLargeScreen) return null;
    
    return drawer;
  }
}

/// 响应式容器
class ResponsiveContainer extends StatelessWidget {
  /// 子组件
  final Widget child;
  
  /// 响应式内边距
  final ResponsiveValue<EdgeInsets>? padding;
  
  /// 响应式外边距
  final ResponsiveValue<EdgeInsets>? margin;
  
  /// 响应式宽度
  final ResponsiveValue<double>? width;
  
  /// 响应式高度
  final ResponsiveValue<double>? height;
  
  /// 响应式装饰
  final ResponsiveValue<Decoration>? decoration;
  
  /// 响应式对齐方式
  final ResponsiveValue<AlignmentGeometry>? alignment;

  const ResponsiveContainer({
    super.key,
    required this.child,
    this.padding,
    this.margin,
    this.width,
    this.height,
    this.decoration,
    this.alignment,
  });

  @override
  Widget build(BuildContext context) {
    final screenType = context.screenType;
    
    return Container(
      padding: padding?.getValue(screenType),
      margin: margin?.getValue(screenType),
      width: width?.getValue(screenType),
      height: height?.getValue(screenType),
      decoration: decoration?.getValue(screenType),
      alignment: alignment?.getValue(screenType),
      child: child,
    );
  }
}

/// 响应式网格
class ResponsiveGrid extends StatelessWidget {
  /// 子组件列表
  final List<Widget> children;
  
  /// 移动端列数
  final int mobileColumns;
  
  /// 平板端列数
  final int tabletColumns;
  
  /// 桌面端列数
  final int desktopColumns;
  
  /// 电视端列数
  final int tvColumns;
  
  /// 主轴间距
  final double mainAxisSpacing;
  
  /// 交叉轴间距
  final double crossAxisSpacing;
  
  /// 子项宽高比
  final double childAspectRatio;
  
  /// 是否收缩包装
  final bool shrinkWrap;
  
  /// 滚动物理效果
  final ScrollPhysics? physics;

  const ResponsiveGrid({
    super.key,
    required this.children,
    this.mobileColumns = 1,
    this.tabletColumns = 2,
    this.desktopColumns = 3,
    this.tvColumns = 4,
    this.mainAxisSpacing = 8.0,
    this.crossAxisSpacing = 8.0,
    this.childAspectRatio = 1.0,
    this.shrinkWrap = false,
    this.physics,
  });

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final width = constraints.maxWidth;
        final screenType = Breakpoints.getScreenType(width);
        
        int columns;
        switch (screenType) {
          case ScreenType.mobile:
            columns = mobileColumns;
            break;
          case ScreenType.tablet:
            columns = tabletColumns;
            break;
          case ScreenType.desktop:
            columns = desktopColumns;
            break;
          case ScreenType.tv:
            columns = tvColumns;
            break;
        }
        
        return GridView.builder(
          shrinkWrap: shrinkWrap,
          physics: physics,
          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: columns,
            mainAxisSpacing: mainAxisSpacing,
            crossAxisSpacing: crossAxisSpacing,
            childAspectRatio: childAspectRatio,
          ),
          itemCount: children.length,
          itemBuilder: (context, index) => children[index],
        );
      },
    );
  }
}

/// 响应式文本
class ResponsiveText extends StatelessWidget {
  /// 文本内容
  final String text;
  
  /// 响应式文本样式
  final ResponsiveValue<TextStyle>? style;
  
  /// 文本对齐方式
  final TextAlign? textAlign;
  
  /// 最大行数
  final int? maxLines;
  
  /// 溢出处理
  final TextOverflow? overflow;

  const ResponsiveText(
    this.text, {
    super.key,
    this.style,
    this.textAlign,
    this.maxLines,
    this.overflow,
  });

  @override
  Widget build(BuildContext context) {
    final screenType = context.screenType;
    
    return Text(
      text,
      style: style?.getValue(screenType),
      textAlign: textAlign,
      maxLines: maxLines,
      overflow: overflow,
    );
  }
}
