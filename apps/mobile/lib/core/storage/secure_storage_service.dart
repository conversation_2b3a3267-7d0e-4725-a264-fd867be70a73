import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:injectable/injectable.dart';
import 'package:flutter/foundation.dart';
import '../logging/app_logger.dart';

/// 安全存储服务
///
/// 提供加密的本地存储功能，用于存储敏感信息如Token、密码等
/// 使用系统级加密，确保数据安全
/// 支持所有平台，包括 Web 平台的特殊处理
@lazySingleton
class SecureStorageService {
  late final FlutterSecureStorage _storage;

  // 构造函数中进行平台检查和配置
  SecureStorageService() {
    if (kIsWeb) {
      // Web 平台专用配置
      _storage = const FlutterSecureStorage(
        webOptions: WebOptions(
          dbName: 'flutter_scaffold_secure_db',
          publicKey: 'flutter_scaffold_public_key',
        ),
      );

      if (kDebugMode) {
        appLogger.info('SecureStorageService: Initialized for Web platform');
      }
    } else {
      // 其他平台使用完整配置
      _storage = const FlutterSecureStorage(
        aOptions: AndroidOptions(
          encryptedSharedPreferences: true,
          sharedPreferencesName: 'flutter_scaffold_secure_prefs',
          preferencesKeyPrefix: 'flutter_scaffold_',
        ),
        iOptions: IOSOptions(
          groupId: 'group.com.example.flutter_scaffold',
          accountName: 'flutter_scaffold_keychain',
          accessibility: KeychainAccessibility.first_unlock_this_device,
        ),
        lOptions: LinuxOptions(),
        wOptions: WindowsOptions(
          useBackwardCompatibility: true,
        ),
        mOptions: MacOsOptions(
          groupId: 'group.com.example.flutter_scaffold',
          accountName: 'flutter_scaffold_keychain',
          accessibility: KeychainAccessibility.first_unlock_this_device,
        ),
      );

      if (kDebugMode) {
        appLogger.info('SecureStorageService: Initialized for native platform');
      }
    }
  }

  // Token 相关常量
  static const String _accessTokenKey = 'access_token';
  static const String _refreshTokenKey = 'refresh_token';
  static const String _tokenExpiryKey = 'token_expiry';
  static const String _userIdKey = 'user_id';

  // 生物识别相关
  static const String _biometricEnabledKey = 'biometric_enabled';
  static const String _biometricTokenKey = 'biometric_token';

  /// 存储访问令牌
  Future<void> storeAccessToken(String token) async {
    try {
      await _storage.write(key: _accessTokenKey, value: token);

      if (kDebugMode) {
        appLogger.info('SecureStorageService: Access token stored successfully on ${kIsWeb ? 'Web' : 'Native'} platform');
      }
    } catch (e) {
      if (kDebugMode) {
        appLogger.error('SecureStorageService: Failed to store access token on ${kIsWeb ? 'Web' : 'Native'} platform', e);
      }

      // Web 平台的特殊错误处理
      if (kIsWeb && e.toString().contains('path_provider')) {
        throw Exception('Web platform storage initialization failed. Please check browser compatibility.');
      }

      rethrow;
    }
  }

  /// 获取访问令牌
  Future<String?> getAccessToken() async {
    try {
      final token = await _storage.read(key: _accessTokenKey);

      if (kDebugMode && token != null) {
        appLogger.info('SecureStorageService: Access token retrieved successfully on ${kIsWeb ? 'Web' : 'Native'} platform');
      }

      return token;
    } catch (e) {
      if (kDebugMode) {
        appLogger.error('SecureStorageService: Failed to get access token on ${kIsWeb ? 'Web' : 'Native'} platform', e);
      }
      return null;
    }
  }

  /// 存储刷新令牌
  Future<void> storeRefreshToken(String token) async {
    try {
      await _storage.write(key: _refreshTokenKey, value: token);
    } catch (e) {
      if (kDebugMode) {
        appLogger.error('SecureStorageService: Failed to store refresh token', e);
      }
      rethrow;
    }
  }

  /// 获取刷新令牌
  Future<String?> getRefreshToken() async {
    try {
      return await _storage.read(key: _refreshTokenKey);
    } catch (e) {
      if (kDebugMode) {
        appLogger.error('SecureStorageService: Failed to get refresh token', e);
      }
      return null;
    }
  }

  /// 存储令牌过期时间
  Future<void> storeTokenExpiry(String expiry) async {
    try {
      await _storage.write(key: _tokenExpiryKey, value: expiry);
    } catch (e) {
      if (kDebugMode) {
        appLogger.error('SecureStorageService: Failed to store token expiry', e);
      }
      rethrow;
    }
  }

  /// 获取令牌过期时间
  Future<String?> getTokenExpiry() async {
    try {
      return await _storage.read(key: _tokenExpiryKey);
    } catch (e) {
      if (kDebugMode) {
        appLogger.error('SecureStorageService: Failed to get token expiry', e);
      }
      return null;
    }
  }

  /// 存储用户ID
  Future<void> storeUserId(String userId) async {
    try {
      await _storage.write(key: _userIdKey, value: userId);
    } catch (e) {
      if (kDebugMode) {
        appLogger.error('SecureStorageService: Failed to store user ID', e);
      }
      rethrow;
    }
  }

  /// 获取用户ID
  Future<String?> getUserId() async {
    try {
      return await _storage.read(key: _userIdKey);
    } catch (e) {
      if (kDebugMode) {
        appLogger.error('SecureStorageService: Failed to get user ID', e);
      }
      return null;
    }
  }

  /// 批量存储认证信息
  Future<void> storeAuthData({
    required String accessToken,
    required String refreshToken,
    required String tokenExpiry,
    required String userId,
  }) async {
    try {
      await Future.wait([
        storeAccessToken(accessToken),
        storeRefreshToken(refreshToken),
        storeTokenExpiry(tokenExpiry),
        storeUserId(userId),
      ]);

      if (kDebugMode) {
        appLogger.info('SecureStorageService: Auth data stored successfully on ${kIsWeb ? 'Web' : 'Native'} platform');
      }
    } catch (e) {
      if (kDebugMode) {
        appLogger.error('SecureStorageService: Failed to store auth data', e);
      }
      rethrow;
    }
  }

  /// 清除所有认证数据
  Future<void> clearAuthData() async {
    try {
      await Future.wait([
        _storage.delete(key: _accessTokenKey),
        _storage.delete(key: _refreshTokenKey),
        _storage.delete(key: _tokenExpiryKey),
        _storage.delete(key: _userIdKey),
      ]);

      if (kDebugMode) {
        appLogger.info('SecureStorageService: Auth data cleared successfully on ${kIsWeb ? 'Web' : 'Native'} platform');
      }
    } catch (e) {
      if (kDebugMode) {
        appLogger.error('SecureStorageService: Failed to clear auth data', e);
      }
      rethrow;
    }
  }

  /// 检查是否有有效的认证数据
  Future<bool> hasValidAuthData() async {
    final accessToken = await getAccessToken();
    final tokenExpiry = await getTokenExpiry();

    if (accessToken == null || tokenExpiry == null) {
      return false;
    }

    // 检查令牌是否过期
    try {
      final expiryDate = DateTime.parse(tokenExpiry);
      return DateTime.now().isBefore(expiryDate);
    } catch (e) {
      return false;
    }
  }

  /// 启用生物识别
  Future<void> enableBiometric(String biometricToken) async {
    await Future.wait([
      _storage.write(key: _biometricEnabledKey, value: 'true'),
      _storage.write(key: _biometricTokenKey, value: biometricToken),
    ]);
  }

  /// 禁用生物识别
  Future<void> disableBiometric() async {
    await Future.wait([
      _storage.delete(key: _biometricEnabledKey),
      _storage.delete(key: _biometricTokenKey),
    ]);
  }

  /// 检查是否启用生物识别
  Future<bool> isBiometricEnabled() async {
    final enabled = await _storage.read(key: _biometricEnabledKey);
    return enabled == 'true';
  }

  /// 获取生物识别令牌
  Future<String?> getBiometricToken() async {
    return await _storage.read(key: _biometricTokenKey);
  }

  /// 存储敏感配置
  Future<void> storeSecureConfig(String key, String value) async {
    await _storage.write(key: 'config_$key', value: value);
  }

  /// 获取敏感配置
  Future<String?> getSecureConfig(String key) async {
    return await _storage.read(key: 'config_$key');
  }

  /// 删除敏感配置
  Future<void> deleteSecureConfig(String key) async {
    await _storage.delete(key: 'config_$key');
  }

  /// 清除所有数据
  Future<void> clearAll() async {
    await _storage.deleteAll();
  }

  /// 获取所有存储的键
  Future<Map<String, String>> getAllData() async {
    return await _storage.readAll();
  }

  /// 检查存储是否可用
  Future<bool> isStorageAvailable() async {
    try {
      await _storage.write(key: 'test_key', value: 'test_value');
      final value = await _storage.read(key: 'test_key');
      await _storage.delete(key: 'test_key');
      return value == 'test_value';
    } catch (e) {
      return false;
    }
  }
}
