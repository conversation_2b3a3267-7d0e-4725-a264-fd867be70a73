import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:injectable/injectable.dart';
import 'failure.dart';
import '../logging/app_logger.dart';

/// 简化的错误处理器
///
/// 提供统一的错误处理逻辑，将各种异常转换为业务层可理解的 Failure 对象
@lazySingleton
class ErrorHandler {

  /// 处理网络错误
  static Failure handleNetworkError(dynamic error, {String? context}) {
    if (kDebugMode) {
      appLogger.error('Network error occurred', error);
    }

    if (error is DioException) {
      return _handleDioException(error);
    }

    return UnknownFailure(
      message: 'Network error: ${error.toString()}',
      originalError: error,
    );
  }

  /// 处理 Dio 异常的私有方法
  static Failure _handleDioException(DioException error) {
    switch (error.type) {
      case DioExceptionType.connectionTimeout:
        return NetworkFailure(
          message: 'Connection timeout',
          originalError: error,
        );
      case DioExceptionType.receiveTimeout:
        return NetworkFailure(
          message: 'Receive timeout',
          originalError: error,
        );
      case DioExceptionType.sendTimeout:
        return NetworkFailure(
          message: 'Send timeout',
          originalError: error,
        );
      case DioExceptionType.badResponse:
        return _handleHttpStatusCode(error.response?.statusCode, error);
      case DioExceptionType.cancel:
        return NetworkFailure(
          message: 'Request cancelled',
          originalError: error,
        );
      case DioExceptionType.connectionError:
        return NetworkFailure(
          message: 'Connection error',
          originalError: error,
        );
      default:
        return NetworkFailure(
          message: 'Unknown network error',
          originalError: error,
        );
    }
  }

  /// 根据 HTTP 状态码处理错误
  static Failure _handleHttpStatusCode(int? statusCode, DioException error) {
    switch (statusCode) {
      case 400:
        return ValidationFailure(
          message: 'Bad request',
          code: statusCode,
          originalError: error,
        );
      case 401:
        return UnauthorizedFailure(
          message: 'Unauthorized',
          code: statusCode,
          originalError: error,
        );
      case 403:
        return UnauthorizedFailure(
          message: 'Forbidden',
          code: statusCode,
          originalError: error,
        );
      case 404:
        return ServerFailure(
          message: 'Resource not found',
          code: statusCode,
          originalError: error,
        );
      case 422:
        return ValidationFailure(
          message: 'Validation failed',
          code: statusCode,
          originalError: error,
        );
      case 500:
        return ServerFailure(
          message: 'Internal server error',
          code: statusCode,
          originalError: error,
        );
      default:
        return ServerFailure(
          message: 'Server error: $statusCode',
          code: statusCode,
          originalError: error,
        );
    }
  }

  /// 通用错误处理方法
  static Failure handleError(dynamic error, {String? context}) {
    if (kDebugMode) {
      appLogger.error('General error occurred', error);
    }

    if (error is DioException) {
      return handleNetworkError(error, context: context);
    }

    if (error is FormatException) {
      return ValidationFailure(
        message: 'Data format error: ${error.message}',
        originalError: error,
      );
    }

    return UnknownFailure(
      message: 'Unknown error: ${error.toString()}',
      originalError: error,
    );
  }

  /// 判断错误是否可重试
  static bool isRetryable(Failure failure) {
    if (failure is NetworkFailure) {
      return true; // 网络错误通常可以重试
    }

    if (failure is ServerFailure) {
      // 5xx错误可以重试，4xx错误通常不可以
      final code = failure.code;
      return code != null && code >= 500;
    }

    return false;
  }

  /// 获取用户友好的错误消息
  static String getUserFriendlyMessage(Failure failure) {
    if (kReleaseMode) {
      // 生产环境返回用户友好的消息
      if (failure is NetworkFailure) {
        return '网络连接异常，请检查网络设置后重试';
      } else if (failure is UnauthorizedFailure) {
        return '登录已过期，请重新登录';
      } else if (failure is ValidationFailure) {
        return '输入信息有误，请检查后重试';
      } else {
        return '服务暂时不可用，请稍后重试';
      }
    }

    // 开发环境返回详细错误信息
    return failure.message;
  }

  /// 记录错误到外部服务（生产环境）
  static void reportError(Failure failure) {
    if (kReleaseMode) {
      // 在生产环境中可以集成外部错误报告服务
      // 例如：Firebase Crashlytics、Sentry等
      appLogger.error('Error reported to external service: ${failure.message}');
    }
  }
}
