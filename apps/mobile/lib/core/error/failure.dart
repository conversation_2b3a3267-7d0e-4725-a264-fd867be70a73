import 'package:equatable/equatable.dart';
import 'package:fpdart/fpdart.dart';

sealed class Failure extends Equatable {
  final String message;
  final int? code;
  final dynamic originalError;

  const Failure({
    required this.message,
    this.code,
    this.originalError,
  });

  @override
  List<Object?> get props => [message, code, originalError];
}

class ServerFailure extends Failure {
  const ServerFailure({
    required super.message,
    super.code,
    super.originalError,
  });
}

class CacheFailure extends Failure {
  const CacheFailure({
    required super.message,
    super.code = 500,
    super.originalError,
  });
}

class NetworkFailure extends Failure {
  const NetworkFailure({
    required super.message,
    super.code = 503,
    super.originalError,
  });
}

class ValidationFailure extends Failure {
  final Map<String, dynamic>? validationErrors;

  const ValidationFailure({
    required super.message,
    super.code = 400,
    super.originalError,
    this.validationErrors,
  });

  @override
  List<Object?> get props => [message, code, originalError, validationErrors];
}

class UnauthorizedFailure extends Failure {
  const UnauthorizedFailure({
    required super.message,
    super.code = 401,
    super.originalError,
  });
}

class NotFoundFailure extends Failure {
  const NotFoundFailure({
    required super.message,
    super.code = 404,
    super.originalError,
  });
}

class UnknownFailure extends Failure {
  const UnknownFailure({
    required super.message,
    super.code = 500,
    super.originalError,
  });
}

class BusinessLogicFailure extends Failure {
  const BusinessLogicFailure({
    required super.message,
    super.code,
    super.originalError,
  });
}

class DatabaseFailure extends Failure {
  const DatabaseFailure({
    required super.message,
    super.code,
    super.originalError,
  });
}

typedef ResultFuture<T> = Future<Either<Failure, T>>;
typedef ResultVoid = ResultFuture<void>;
typedef StreamResult<T> = Stream<Either<Failure, T>>;