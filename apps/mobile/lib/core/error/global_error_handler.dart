import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import '../theme/theme_extensions.dart';

/// Global error handler for unhandled exceptions
class GlobalErrorHandler {
  static void initialize() {
    // Handle Flutter framework errors
    FlutterError.onError = (FlutterErrorDetails details) {
      _handleFlutterError(details);
    };

    // Handle platform/async errors
    PlatformDispatcher.instance.onError = (error, stack) {
      _handlePlatformError(error, stack);
      return true;
    };
  }

  static void _handleFlutterError(FlutterErrorDetails details) {
    // Log error details
    debugPrint('Flutter Error: ${details.exception}');
    debugPrint('Stack trace: ${details.stack}');

    // Report to crash analytics in production
    // FirebaseCrashlytics.instance.recordFlutterFatalError(details);

    // Show user-friendly error in debug mode
    if (kDebugMode) {
      FlutterError.presentError(details);
    }
  }

  static bool _handlePlatformError(Object error, StackTrace stack) {
    // Log platform error
    debugPrint('Platform Error: $error');
    debugPrint('Stack trace: $stack');

    // Report to crash analytics
    // FirebaseCrashlytics.instance.recordError(error, stack, fatal: true);

    return true;
  }

  /// Show a user-friendly error dialog
  static void showErrorDialog(BuildContext context, String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('错误'),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }

  /// Show error snackbar
  static void showErrorSnackBar(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: context.themeColors.danger,
        action: SnackBarAction(
          label: '关闭',
          textColor: context.themeColors.danger.onColor,
          onPressed: () {
            ScaffoldMessenger.of(context).hideCurrentSnackBar();
          },
        ),
      ),
    );
  }
}

/// Custom error widget for graceful error display
class CustomErrorWidget extends StatelessWidget {
  final FlutterErrorDetails errorDetails;

  const CustomErrorWidget({
    super.key,
    required this.errorDetails,
  });

  @override
  Widget build(BuildContext context) {
    return Material(
      child: Container(
        padding: const EdgeInsets.all(16.0),
        color: context.themeColors.dangerLight,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: context.themeColors.danger,
            ),
            const SizedBox(height: 16),
            Text(
              '出现了一些问题',
              style: context.textStyles.heading5?.copyWith(
                color: context.colors.onSurface,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              '请重启应用或联系客服',
              style: context.textStyles.body?.copyWith(
                color: context.colors.onSurfaceVariant,
              ),
            ),
            if (kDebugMode) ...[
              const SizedBox(height: 16),
              Expanded(
                child: SingleChildScrollView(
                  child: Text(
                    errorDetails.toString(),
                    style: const TextStyle(
                      fontSize: 10,
                      fontFamily: 'monospace',
                    ),
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}