// dart format width=80
// GENERATED CODE - DO NOT MODIFY BY HAND

// **************************************************************************
// InjectableConfigGenerator
// **************************************************************************

// ignore_for_file: type=lint
// coverage:ignore-file

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'package:dio/dio.dart' as _i361;
import 'package:get_it/get_it.dart' as _i174;
import 'package:injectable/injectable.dart' as _i526;
import 'package:shared_preferences/shared_preferences.dart' as _i460;

import '../../features/auth/data/datasources/auth_remote_data_source.dart'
    as _i107;
import '../../features/auth/data/repositories/auth_repository_impl.dart'
    as _i153;
import '../../features/auth/domain/repositories/auth_repository.dart' as _i787;
import '../../features/auth/domain/usecases/get_current_user_usecase.dart'
    as _i17;
import '../../features/auth/domain/usecases/login_usecase.dart' as _i188;
import '../../features/auth/domain/usecases/logout_usecase.dart' as _i48;
import '../../features/auth/domain/usecases/send_sms_code_usecase.dart'
    as _i613;
import '../../features/auth/domain/usecases/sms_login_usecase.dart' as _i465;
import '../../features/auth/domain/usecases/wechat_login_usecase.dart' as _i912;
import '../../features/auth/presentation/bloc/auth_bloc.dart' as _i797;
import '../auth/token_manager.dart' as _i428;
import '../error/error_handler.dart' as _i308;
import '../network/dio_client.dart' as _i667;
import '../storage/secure_storage_service.dart' as _i666;
import '../theme/theme_bloc.dart' as _i118;
import '../theme/theme_manager.dart' as _i121;
import 'di_module.dart' as _i211;

// initializes the registration of main-scope dependencies inside of GetIt
Future<_i174.GetIt> $initGetIt(
  _i174.GetIt getIt, {
  String? environment,
  _i526.EnvironmentFilter? environmentFilter,
}) async {
  final gh = _i526.GetItHelper(getIt, environment, environmentFilter);
  final injectableModule = _$InjectableModule();
  await gh.factoryAsync<_i460.SharedPreferences>(
    () => injectableModule.prefs,
    preResolve: true,
  );
  gh.lazySingleton<_i667.DioClient>(() => _i667.DioClient());
  gh.lazySingleton<_i666.SecureStorageService>(
    () => _i666.SecureStorageService(),
  );
  gh.lazySingleton<_i308.ErrorHandler>(() => _i308.ErrorHandler());
  gh.lazySingleton<_i121.ThemeManager>(
    () => _i121.ThemeManager(gh<_i666.SecureStorageService>()),
  );
  gh.lazySingleton<_i428.TokenManager>(
    () => _i428.TokenManager(gh<_i666.SecureStorageService>()),
  );
  gh.factory<_i118.ThemeBloc>(() => _i118.ThemeBloc(gh<_i121.ThemeManager>()));
  await gh.lazySingletonAsync<_i361.Dio>(
    () => injectableModule.dio(gh<_i428.TokenManager>()),
    preResolve: true,
  );
  gh.lazySingleton<_i107.AuthRemoteDataSource>(
    () => _i107.AuthRemoteDataSource(gh<_i361.Dio>()),
  );
  gh.lazySingleton<_i787.AuthRepository>(
    () => _i153.AuthRepositoryImpl(
      remoteDataSource: gh<_i107.AuthRemoteDataSource>(),
      sharedPreferences: gh<_i460.SharedPreferences>(),
      tokenManager: gh<_i428.TokenManager>(),
    ),
  );
  gh.factory<_i188.LoginUseCase>(
    () => _i188.LoginUseCase(gh<_i787.AuthRepository>()),
  );
  gh.factory<_i48.LogoutUseCase>(
    () => _i48.LogoutUseCase(gh<_i787.AuthRepository>()),
  );
  gh.factory<_i465.SmsLoginUseCase>(
    () => _i465.SmsLoginUseCase(gh<_i787.AuthRepository>()),
  );
  gh.factory<_i613.SendSmsCodeUseCase>(
    () => _i613.SendSmsCodeUseCase(gh<_i787.AuthRepository>()),
  );
  gh.factory<_i912.WechatLoginUseCase>(
    () => _i912.WechatLoginUseCase(gh<_i787.AuthRepository>()),
  );
  gh.factory<_i17.GetCurrentUserUseCase>(
    () => _i17.GetCurrentUserUseCase(gh<_i787.AuthRepository>()),
  );
  gh.factory<_i797.AuthBloc>(
    () => _i797.AuthBloc(
      loginUseCase: gh<_i188.LoginUseCase>(),
      logoutUseCase: gh<_i48.LogoutUseCase>(),
      getCurrentUserUseCase: gh<_i17.GetCurrentUserUseCase>(),
      smsLoginUseCase: gh<_i465.SmsLoginUseCase>(),
      sendSmsCodeUseCase: gh<_i613.SendSmsCodeUseCase>(),
      wechatLoginUseCase: gh<_i912.WechatLoginUseCase>(),
    ),
  );
  return getIt;
}

class _$InjectableModule extends _i211.InjectableModule {}
