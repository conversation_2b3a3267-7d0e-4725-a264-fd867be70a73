import 'package:dio/dio.dart';
import 'package:injectable/injectable.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../network/network_factory.dart';
import '../auth/token_manager.dart';

@module
abstract class InjectableModule {
  @lazySingleton
  @preResolve
  Future<Dio> dio(TokenManager tokenManager) => NetworkFactory.createConfiguredDio(tokenManager);

  @preResolve
  Future<SharedPreferences> get prefs => SharedPreferences.getInstance();
}
