import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

import '../../../../core/logging/app_logger.dart';
import '../../../../core/performance/performance_manager.dart';
import '../../../../core/shared/constants.dart';
import '../../../../core/theme/theme_extensions.dart';

/// 应用主页
///
/// 提供应用的主要功能入口和用户信息展示
class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _initializePage();
  }

  Future<void> _initializePage() async {
    setState(() => _isLoading = true);

    try {
      // 模拟数据加载
      await Future.delayed(const Duration(milliseconds: 500));

      // 生成性能报告
      PerformanceManager().generatePerformanceReport();

    } catch (e) {
      appLogger.error('HomePage initialization failed', e);
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Flutter Scaffold'),
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.palette),
            tooltip: '主题设置',
            onPressed: () {
              context.push(AppConstants.themeSelectionRoute);
            },
          ),
          IconButton(
            icon: const Icon(Icons.person),
            tooltip: '个人资料',
            onPressed: () {
              context.push(AppConstants.profileRoute);
            },
          ),
          const SizedBox(width: 8),
        ],
      ),
      body: _isLoading
        ? const Center(child: CircularProgressIndicator())
        : RefreshIndicator(
            onRefresh: _initializePage,
            child: SingleChildScrollView(
              physics: const AlwaysScrollableScrollPhysics(),
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildWelcomeCard(),
                  const SizedBox(height: 16),
                  _buildQuickActions(),
                  const SizedBox(height: 16),
                  _buildFeatureCards(),
                  const SizedBox(height: 16),
                  _buildPerformanceInfo(),
                ],
              ),
            ),
          ),
    );
  }

  /// 构建欢迎卡片
  Widget _buildWelcomeCard() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: context.colors.primary.light,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    Icons.home,
                    size: 32,
                    color: context.colors.primary,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '欢迎使用 Flutter Scaffold',
                        style: context.textStyles.heading5?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        '现代化的 Flutter 应用开发脚手架',
                        style: context.textStyles.body?.copyWith(
                          color: context.themeColors.neutral600,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: context.themeColors.success.light,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: context.themeColors.success.medium),
              ),
              child: Row(
                children: [
                  Icon(Icons.check_circle, color: context.themeColors.success, size: 20),
                  const SizedBox(width: 8),
                  Text(
                    '性能优化系统已激活',
                    style: TextStyle(
                      color: context.themeColors.success,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建快速操作
  Widget _buildQuickActions() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '快速操作',
          style: context.textStyles.heading6?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: _buildActionButton(
                icon: Icons.palette,
                label: '主题设置',
                onTap: () => context.push(AppConstants.themeSelectionRoute),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildActionButton(
                icon: Icons.person,
                label: '个人资料',
                onTap: () => context.push(AppConstants.profileRoute),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildActionButton(
                icon: Icons.settings,
                label: '应用设置',
                onTap: () {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('设置功能即将推出')),
                  );
                },
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// 构建操作按钮
  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 12),
        decoration: BoxDecoration(
          border: Border.all(color: context.themeColors.neutral300),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          children: [
            Icon(icon, size: 28, color: context.colors.primary),
            const SizedBox(height: 8),
            Text(
              label,
              style: context.textStyles.caption,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  /// 构建功能卡片
  Widget _buildFeatureCards() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '核心功能',
          style: context.textStyles.heading6?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        _buildFeatureCard(
          icon: Icons.security,
          title: '安全认证',
          description: '基于JWT的安全认证系统，支持自动刷新和状态管理',
          color: context.themeColors.info,
        ),
        const SizedBox(height: 12),
        _buildFeatureCard(
          icon: Icons.speed,
          title: '性能优化',
          description: '内置性能监控和优化系统，自动优化内存、网络和渲染性能',
          color: context.themeColors.success,
        ),
        const SizedBox(height: 12),
        _buildFeatureCard(
          icon: Icons.architecture,
          title: 'Clean Architecture',
          description: '严格遵循Clean Architecture架构，代码结构清晰，易于维护',
          color: context.themeColors.warning,
        ),
      ],
    );
  }

  /// 构建功能卡片
  Widget _buildFeatureCard({
    required IconData icon,
    required String title,
    required String description,
    required Color color,
  }) {
    return Card(
      elevation: 1,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: color.withLowOpacity,
                borderRadius: BorderRadius.circular(10),
              ),
              child: Icon(icon, color: color, size: 24),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: context.textStyles.subtitle?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    description,
                    style: context.textStyles.caption?.copyWith(
                      color: context.themeColors.neutral600,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建性能信息
  Widget _buildPerformanceInfo() {
    return Card(
      elevation: 1,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.analytics, color: context.colors.primary),
                const SizedBox(width: 8),
                Text(
                  '性能监控',
                  style: context.textStyles.subtitle?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              '应用性能监控系统正在运行，实时优化内存使用、网络请求和渲染性能。',
              style: context.textStyles.caption?.copyWith(
                color: context.themeColors.neutral600,
              ),
            ),
            const SizedBox(height: 12),
            ElevatedButton.icon(
              onPressed: () {
                PerformanceManager().generatePerformanceReport();
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('性能报告已生成，请查看控制台输出'),
                  ),
                );
              },
              icon: const Icon(Icons.assessment, size: 18),
              label: const Text('生成性能报告'),
              style: ElevatedButton.styleFrom(
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
