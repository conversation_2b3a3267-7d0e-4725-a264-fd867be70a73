part of 'auth_bloc.dart';

enum AuthStatus {
  initial,
  loading,
  authenticated,
  unauthenticated,
  codeSent,
  wechatLoading,
}

class AuthState extends Equatable {
  final AuthStatus status;
  final AuthUser? user;
  final String? error;
  final DateTime? lastUpdated;

  const AuthState({
    this.status = AuthStatus.initial,
    this.user,
    this.error,
    this.lastUpdated,
  });

  AuthState copyWith({
    AuthStatus? status,
    AuthUser? user,
    String? error,
    DateTime? lastUpdated,
    bool clearError = false,
    bool clearUser = false,
  }) {
    return AuthState(
      status: status ?? this.status,
      user: clearUser ? null : (user ?? this.user),
      error: clearError ? null : (error ?? this.error),
      lastUpdated: lastUpdated ?? DateTime.now(),
    );
  }

  @override
  List<Object?> get props => [status, user, error, lastUpdated];

  // 便捷的状态检查方法
  bool get isLoading => status == AuthStatus.loading || status == AuthStatus.wechatLoading;
  bool get isAuthenticated => status == AuthStatus.authenticated && user != null;
  bool get isUnauthenticated => status == AuthStatus.unauthenticated;
  bool get hasError => error != null && error!.isNotEmpty;
  bool get isCodeSent => status == AuthStatus.codeSent;
  bool get isWechatLoading => status == AuthStatus.wechatLoading;
  bool get isInitial => status == AuthStatus.initial;

  // 用户信息快捷访问
  String? get userId => user?.id;
  String? get userEmail => user?.email;
  String? get userName => user?.name;
  String? get userAvatar => user?.avatar;

  // 状态持续时间
  Duration? get stateDuration {
    if (lastUpdated == null) return null;
    return DateTime.now().difference(lastUpdated!);
  }

  // 是否需要刷新
  bool get needsRefresh {
    if (lastUpdated == null) return true;
    return DateTime.now().difference(lastUpdated!).inMinutes > 30;
  }

  @override
  String toString() {
    return 'AuthState(status: $status, user: ${user?.email ?? 'null'}, error: $error, lastUpdated: $lastUpdated)';
  }
}