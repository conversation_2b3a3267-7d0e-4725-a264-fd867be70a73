import 'package:freezed_annotation/freezed_annotation.dart';
import '../../domain/entities/auth_user.dart';

part 'user_model.freezed.dart';
part 'user_model.g.dart';

/// Data Transfer Object for User
///
/// This model represents user data as received from external sources (API, database).
/// It handles JSON serialization/deserialization and provides conversion to domain entities.
///
/// Features:
/// - JSON serialization with custom field mapping
/// - Conversion to/from domain entities
/// - Immutable data structure using Freezed
/// - Null safety support
@freezed
abstract class UserModel with _$UserModel {
  const UserModel._();

  const factory UserModel({
    required String id,
    required String email,
    String? name,
    String? phone,
    String? avatar,
    @Json<PERSON>ey(name: 'created_at') DateTime? createdAt,
    @JsonKey(name: 'updated_at') DateTime? updatedAt,
    // Auth-related fields - mapped from API response
    @JsonKey(name: 'access_token') String? accessToken,
    @Json<PERSON>ey(name: 'refresh_token') String? refreshToken,
    @JsonKey(name: 'token_expiry') String? tokenExpiry,
  }) = _UserModel;

  factory UserModel.fromJson(Map<String, dynamic> json) => _$UserModelFromJson(json);

  /// Convert Model to Domain Entity
  ///
  /// This method transforms the data model into a domain entity,
  /// following Clean Architecture principles.
  User toEntity() {
    return User(
      id: id,
      email: email,
      name: name,
      phone: phone,
      avatar: avatar,
      createdAt: createdAt,
      updatedAt: updatedAt,
      accessToken: accessToken,
      refreshToken: refreshToken,
      tokenExpiry: tokenExpiry,
    );
  }

  /// Create Model from Domain Entity
  ///
  /// This factory method creates a data model from a domain entity,
  /// useful for preparing data for external storage or transmission.
  factory UserModel.fromEntity(User entity) {
    return UserModel(
      id: entity.id,
      email: entity.email,
      name: entity.name,
      phone: entity.phone,
      avatar: entity.avatar,
      createdAt: entity.createdAt,
      updatedAt: entity.updatedAt,
      accessToken: entity.accessToken,
      refreshToken: entity.refreshToken,
      tokenExpiry: entity.tokenExpiry,
    );
  }

  /// Create Model from Auth Response
  ///
  /// Specialized factory for creating user models from authentication responses
  /// that may include tokens and other auth-specific data.
  factory UserModel.fromAuthResponse({
    required String id,
    required String email,
    String? name,
    String? phone,
    String? avatar,
    String? accessToken,
    String? refreshToken,
    String? tokenExpiry,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return UserModel(
      id: id,
      email: email,
      name: name,
      phone: phone,
      avatar: avatar,
      createdAt: createdAt,
      updatedAt: updatedAt,
      accessToken: accessToken,
      refreshToken: refreshToken,
      tokenExpiry: tokenExpiry,
    );
  }

  /// Create a copy without sensitive auth data
  ///
  /// Useful for logging or caching scenarios where tokens should not be included.
  UserModel withoutTokens() {
    return copyWith(
      accessToken: null,
      refreshToken: null,
      tokenExpiry: null,
    );
  }

  /// Check if this model contains valid auth tokens
  bool get hasValidTokens =>
      accessToken?.isNotEmpty == true &&
      refreshToken?.isNotEmpty == true;
}
