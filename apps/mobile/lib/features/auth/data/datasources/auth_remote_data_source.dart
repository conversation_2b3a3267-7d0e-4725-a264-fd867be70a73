import 'package:dio/dio.dart';
import 'package:injectable/injectable.dart';
import 'package:retrofit/retrofit.dart';

import '../../domain/entities/auth_user.dart';

part 'auth_remote_data_source.g.dart';

@lazySingleton
@RestApi()
abstract class AuthRemoteDataSource {
  @factoryMethod
  factory AuthRemoteDataSource(Dio dio) = _AuthRemoteDataSource;

  @POST('/auth/login')
  Future<User> login(
    @Body() Map<String, dynamic> body,
  );

  @POST('/auth/register')
  Future<User> register(
    @Body() Map<String, dynamic> body,
  );

  @POST('/auth/refresh')
  Future<User> refreshToken(
    @Body() Map<String, dynamic> body,
  );

  @POST('/auth/forgot-password')
  Future<void> forgotPassword(
    @Body() Map<String, dynamic> body,
  );

  @GET('/auth/me')
  Future<User> getCurrentUser(
    @Header('Authorization') String token,
  );

  @POST('/auth/logout')
  Future<void> logout(
    @Header('Authorization') String token,
  );

  @POST('/auth/sms/login')
  Future<User> smsLogin(
    @Body() Map<String, dynamic> body,
  );

  @POST('/auth/sms/send-code')
  Future<void> sendSmsCode(
    @Body() Map<String, dynamic> body,
  );

  @POST('/auth/wechat/login')
  Future<User> wechatLogin();
}