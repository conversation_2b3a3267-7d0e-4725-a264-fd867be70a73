import 'package:dio/dio.dart';
import 'package:injectable/injectable.dart';
import 'package:retrofit/retrofit.dart';

import '../models/user_model.dart';

part 'auth_remote_data_source.g.dart';

@lazySingleton
@RestApi()
abstract class AuthRemoteDataSource {
  @factoryMethod
  factory AuthRemoteDataSource(Dio dio) = _AuthRemoteDataSource;

  @POST('/auth/login')
  Future<UserModel> login(
    @Body() Map<String, dynamic> body,
  );

  @POST('/auth/register')
  Future<UserModel> register(
    @Body() Map<String, dynamic> body,
  );

  @POST('/auth/refresh')
  Future<UserModel> refreshToken(
    @Body() Map<String, dynamic> body,
  );

  @POST('/auth/forgot-password')
  Future<void> forgotPassword(
    @Body() Map<String, dynamic> body,
  );

  @GET('/auth/me')
  Future<UserModel> getCurrentUser(
    @Header('Authorization') String token,
  );

  @POST('/auth/logout')
  Future<void> logout(
    @Header('Authorization') String token,
  );

  @POST('/auth/sms/login')
  Future<UserModel> smsLogin(
    @Body() Map<String, dynamic> body,
  );

  @POST('/auth/sms/send-code')
  Future<void> sendSmsCode(
    @Body() Map<String, dynamic> body,
  );

  @POST('/auth/wechat/login')
  Future<UserModel> wechatLogin();
}