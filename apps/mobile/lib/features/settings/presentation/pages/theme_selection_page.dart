import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../core/theme/theme_bloc.dart';
import '../../../../core/theme/theme_config.dart';
import '../../../../core/theme/theme_extensions.dart';
import '../../../../core/responsive/responsive_layout.dart';
import '../../../../core/di/di.dart';

/// 主题选择页面
class ThemeSelectionPage extends StatelessWidget {
  const ThemeSelectionPage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider.value(
      value: getIt<ThemeBloc>(),
      child: ResponsivePage(
        title: '选择主题',
        child: BlocBuilder<ThemeBloc, ThemeState>(
        builder: (context, state) {
          if (state.isLoading) {
            return const Center(
              child: CircularProgressIndicator(),
            );
          }

          if (state.errorMessage != null) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.error_outline,
                    size: 64,
                    color: context.colors.error,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    '主题加载失败',
                    style: context.textStyles.heading5,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    state.errorMessage!,
                    style: context.textStyles.body?.copyWith(
                      color: context.colors.onSurfaceVariant,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 24),
                  ElevatedButton(
                    onPressed: () {
                      context.read<ThemeBloc>().add(const ThemeInitialized());
                    },
                    child: const Text('重试'),
                  ),
                ],
              ),
            );
          }

          return ListView(
            children: [
              // 系统主题部分
              _buildSectionHeader(context, '系统主题'),
              const SizedBox(height: 12),
              _buildThemeGrid(context, [
                AppTheme.system,
                AppTheme.light,
                AppTheme.dark,
              ], state.config.appTheme),

              const SizedBox(height: 32),

              // Material You 部分
              _buildSectionHeader(context, 'Material You'),
              const SizedBox(height: 8),
              if (!state.config.supportsDynamicColor)
                Padding(
                  padding: const EdgeInsets.only(bottom: 12),
                  child: Text(
                    '您的设备不支持动态颜色功能',
                    style: context.textStyles.caption?.copyWith(
                      color: context.colors.onSurfaceVariant,
                    ),
                  ),
                ),
              const SizedBox(height: 12),
              _buildThemeGrid(context, [
                AppTheme.materialYou,
              ], state.config.appTheme),

              const SizedBox(height: 32),

              // 预设主题部分
              _buildSectionHeader(context, '预设主题'),
              const SizedBox(height: 12),
              _buildThemeGrid(context, [
                AppTheme.businessBlue,
                AppTheme.natureGreen,
                AppTheme.warmOrange,
                AppTheme.elegantPurple,
                AppTheme.classicMono,
              ], state.config.appTheme),

              const SizedBox(height: 32),

              // 自定义主题部分
              _buildSectionHeader(context, '自定义主题'),
              const SizedBox(height: 12),
              _buildThemeGrid(context, [
                AppTheme.custom,
              ], state.config.appTheme),

              const SizedBox(height: 24),
            ],
          );
        },
      ),
      ),
    );
  }

  Widget _buildSectionHeader(BuildContext context, String title) {
    return Text(
      title,
      style: context.textStyles.subtitle?.copyWith(
        fontWeight: FontWeight.w600,
        color: context.colors.primary,
      ),
    );
  }

  Widget _buildThemeGrid(BuildContext context, List<AppTheme> themes, AppTheme currentTheme) {
    return ResponsiveGrid(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      mobileColumns: 2,
      tabletColumns: 3,
      desktopColumns: 4,
      tvColumns: 5,
      mainAxisSpacing: 12,
      crossAxisSpacing: 12,
      childAspectRatio: 1.2,
      children: themes.map((theme) {
        final isSelected = theme == currentTheme;
        final isDisabled = theme == AppTheme.materialYou &&
            !context.read<ThemeBloc>().state.config.supportsDynamicColor;

        return _ThemeCard(
          theme: theme,
          isSelected: isSelected,
          isDisabled: isDisabled,
          onTap: isDisabled ? null : () {
            context.read<ThemeBloc>().add(ThemeChanged(theme));
          },
        );
      }).toList(),
    );
  }
}

class _ThemeCard extends StatelessWidget {
  final AppTheme theme;
  final bool isSelected;
  final bool isDisabled;
  final VoidCallback? onTap;

  const _ThemeCard({
    required this.theme,
    required this.isSelected,
    required this.isDisabled,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final colorScheme = context.colors;

    return Card(
      elevation: isSelected ? 8 : 2,
      shadowColor: isSelected ? colorScheme.primary.withValues(alpha: 0.3) : null,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            border: isSelected
                ? Border.all(
                    color: colorScheme.primary,
                    width: 2,
                  )
                : null,
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // 主题图标
              Container(
                width: 48,
                height: 48,
                decoration: BoxDecoration(
                  color: isDisabled
                      ? colorScheme.surfaceContainerHighest
                      : theme.previewColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(24),
                  border: Border.all(
                    color: isDisabled
                        ? colorScheme.outline.withValues(alpha: 0.5)
                        : theme.previewColor.withValues(alpha: 0.3),
                    width: 2,
                  ),
                ),
                child: Icon(
                  theme.icon,
                  size: 24,
                  color: isDisabled
                      ? colorScheme.onSurfaceVariant.withValues(alpha: 0.5)
                      : theme.previewColor,
                ),
              ),

              const SizedBox(height: 12),

              // 主题名称
              Text(
                theme.displayName,
                style: context.textStyles.subtitle?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: isDisabled
                      ? colorScheme.onSurfaceVariant.withValues(alpha: 0.5)
                      : null,
                ),
                textAlign: TextAlign.center,
              ),

              const SizedBox(height: 4),

              // 主题描述
              Text(
                theme.description,
                style: context.textStyles.caption?.copyWith(
                  color: isDisabled
                      ? colorScheme.onSurfaceVariant.withValues(alpha: 0.5)
                      : colorScheme.onSurfaceVariant,
                ),
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),

              // 选中指示器
              if (isSelected)
                Padding(
                  padding: const EdgeInsets.only(top: 8),
                  child: Icon(
                    Icons.check_circle,
                    color: colorScheme.primary,
                    size: 20,
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }
}
