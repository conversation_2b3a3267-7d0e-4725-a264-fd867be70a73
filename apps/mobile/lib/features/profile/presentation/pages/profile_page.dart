import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

import '../../../../core/logging/app_logger.dart';
import '../../../../core/shared/constants.dart';
import '../../../../core/theme/theme_extensions.dart';
import 'edit_profile_page.dart';

/// 用户资料页面
///
/// 显示用户信息和提供个人设置功能
class ProfilePage extends StatefulWidget {
  const ProfilePage({super.key});

  @override
  State<ProfilePage> createState() => _ProfilePageState();
}

class _ProfilePageState extends State<ProfilePage> {
  bool _isLoading = false;

  // 模拟用户数据
  final Map<String, dynamic> _userInfo = {
    'name': 'Flutter 开发者',
    'email': '<EMAIL>',
    'phone': '+86 138 0013 8000',
    'joinDate': '2024-01-01',
    'avatar': null,
  };

  @override
  void initState() {
    super.initState();
    _loadUserProfile();
  }

  Future<void> _loadUserProfile() async {
    setState(() => _isLoading = true);

    try {
      // 模拟加载用户数据
      await Future.delayed(const Duration(milliseconds: 800));
      appLogger.info('User profile loaded successfully');

    } catch (e) {
      appLogger.error('Failed to load user profile', e);
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('个人资料'),
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.edit),
            tooltip: '编辑资料',
            onPressed: _editProfile,
          ),
        ],
      ),
      body: _isLoading
        ? const Center(child: CircularProgressIndicator())
        : RefreshIndicator(
            onRefresh: _loadUserProfile,
            child: SingleChildScrollView(
              physics: const AlwaysScrollableScrollPhysics(),
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  _buildProfileHeader(),
                  const SizedBox(height: 24),
                  _buildUserInfo(),
                  const SizedBox(height: 24),
                  _buildActionButtons(),
                  const SizedBox(height: 24),
                  _buildSettingsSection(),
                ],
              ),
            ),
          ),
    );
  }

  /// 构建资料头部
  Widget _buildProfileHeader() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          children: [
            Stack(
              children: [
                CircleAvatar(
                  radius: 50,
                  backgroundColor: context.colors.primary.light,
                  child: _userInfo['avatar'] != null
                    ? ClipOval(
                        child: Image.network(
                          _userInfo['avatar'],
                          width: 100,
                          height: 100,
                          fit: BoxFit.cover,
                        ),
                      )
                    : Icon(
                        Icons.person,
                        size: 50,
                        color: context.colors.primary,
                      ),
                ),
                Positioned(
                  bottom: 0,
                  right: 0,
                  child: Container(
                    decoration: BoxDecoration(
                      color: context.colors.primary,
                      shape: BoxShape.circle,
                    ),
                    child: IconButton(
                      icon: Icon(Icons.camera_alt, color: context.colors.onPrimary, size: 20),
                      onPressed: _changeAvatar,
                      constraints: const BoxConstraints(minWidth: 36, minHeight: 36),
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Text(
              _userInfo['name'] ?? '未知用户',
              style: context.textStyles.heading5?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              _userInfo['email'] ?? '',
              style: context.textStyles.body?.copyWith(
                color: context.themeColors.neutral600,
              ),
            ),
            const SizedBox(height: 8),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: context.themeColors.success.light,
                borderRadius: BorderRadius.circular(16),
                border: Border.all(color: context.themeColors.success.medium),
              ),
              child: Text(
                '已验证用户',
                style: TextStyle(
                  color: context.themeColors.success,
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建用户信息
  Widget _buildUserInfo() {
    return Card(
      elevation: 1,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '基本信息',
              style: context.textStyles.subtitle?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            _buildInfoRow(Icons.person, '姓名', _userInfo['name'] ?? '未设置'),
            _buildInfoRow(Icons.email, '邮箱', _userInfo['email'] ?? '未设置'),
            _buildInfoRow(Icons.phone, '手机', _userInfo['phone'] ?? '未设置'),
            _buildInfoRow(Icons.calendar_today, '注册时间', _userInfo['joinDate'] ?? '未知'),
          ],
        ),
      ),
    );
  }

  /// 构建信息行
  Widget _buildInfoRow(IconData icon, String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          Icon(icon, size: 20, color: context.themeColors.neutral600),
          const SizedBox(width: 12),
          SizedBox(
            width: 60,
            child: Text(
              label,
              style: context.textStyles.caption?.copyWith(
                color: context.themeColors.neutral600,
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              value,
              style: context.textStyles.body,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建操作按钮
  Widget _buildActionButtons() {
    return Row(
      children: [
        Expanded(
          child: ElevatedButton.icon(
            onPressed: _editProfile,
            icon: const Icon(Icons.edit, size: 18),
            label: const Text('编辑资料'),
            style: ElevatedButton.styleFrom(
              padding: const EdgeInsets.symmetric(vertical: 12),
            ),
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: OutlinedButton.icon(
            onPressed: _shareProfile,
            icon: const Icon(Icons.share, size: 18),
            label: const Text('分享资料'),
            style: OutlinedButton.styleFrom(
              padding: const EdgeInsets.symmetric(vertical: 12),
            ),
          ),
        ),
      ],
    );
  }

  /// 构建设置区域
  Widget _buildSettingsSection() {
    return Card(
      elevation: 1,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '设置',
              style: context.textStyles.subtitle?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            _buildSettingItem(
              icon: Icons.palette,
              title: '主题设置',
              subtitle: '切换应用主题',
              onTap: () => context.push(AppConstants.themeSelectionRoute),
            ),
            _buildSettingItem(
              icon: Icons.notifications,
              title: '通知设置',
              subtitle: '管理推送通知',
              onTap: () => _showComingSoon('通知设置'),
            ),
            _buildSettingItem(
              icon: Icons.security,
              title: '隐私设置',
              subtitle: '账户安全与隐私',
              onTap: () => _showComingSoon('隐私设置'),
            ),
            _buildSettingItem(
              icon: Icons.help,
              title: '帮助与反馈',
              subtitle: '获取帮助或提供反馈',
              onTap: () => _showComingSoon('帮助与反馈'),
            ),
            _buildSettingItem(
              icon: Icons.logout,
              title: '退出登录',
              subtitle: '安全退出当前账户',
              onTap: _logout,
              isDestructive: true,
            ),
          ],
        ),
      ),
    );
  }

  /// 构建设置项
  Widget _buildSettingItem({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
    bool isDestructive = false,
  }) {
    return ListTile(
      leading: Icon(
        icon,
        color: isDestructive ? context.themeColors.danger : context.colors.primary,
      ),
      title: Text(
        title,
        style: TextStyle(
          color: isDestructive ? context.themeColors.danger : null,
          fontWeight: FontWeight.w500,
        ),
      ),
      subtitle: Text(subtitle),
      trailing: const Icon(Icons.chevron_right),
      onTap: onTap,
      contentPadding: EdgeInsets.zero,
    );
  }

  /// 编辑资料
  Future<void> _editProfile() async {
    final result = await Navigator.of(context).push<bool>(
      MaterialPageRoute(
        builder: (context) => const EditProfilePage(),
      ),
    );

    // 如果有更新，重新加载用户资料
    if (result == true) {
      await _loadUserProfile();
    }
  }

  /// 更换头像
  void _changeAvatar() {
    _showComingSoon('更换头像');
  }

  /// 分享资料
  void _shareProfile() {
    _showComingSoon('分享资料');
  }

  /// 退出登录
  void _logout() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('确认退出'),
        content: const Text('确定要退出登录吗？'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              // TODO: 实现退出登录逻辑
              context.go('/login');
            },
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }

  /// 显示即将推出提示
  void _showComingSoon(String feature) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('$feature功能即将推出')),
    );
  }
}
